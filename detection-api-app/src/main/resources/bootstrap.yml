# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8108
  servlet:
    # 应用的访问路径
    context-path: /
  # undertow 配置
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 8
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 256

ruoyi:
  profile: profile

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn
    # Nacos配置中心核心包日志
    com.alibaba.cloud.nacos.config: debug
    # Nacos服务发现核心包日志
    # com.alibaba.cloud.nacos.discovery: DEBUG
    # Nacos客户端底层通信日志（可选，更详细）
    com.alibaba.nacos.client: debug
    # Spring Cloud与Nacos集成的配置加载日志（可选）
    org.springframework.cloud.alibaba.nacos: DEBUG

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  application:
    name: detection-api-app
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles: 
    active: dev
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  500MB
       # 设置总上传的文件大小
       max-request-size:  1000MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: *************
    port: 36379
    # 数据库索引
    database: 0
    # 密码
    password: sunjie_hFhy22wedfgtfdsxgytrdcvytrdec
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  jackson: #返回json的全局时间格式
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  cloud:
    nacos:
      # 配置中心
      config:
        enabled: true
        server-addr: ${nacos.config.address}
        namespace: ${nacos.config.namespace}
        file-extension: yml
        group: ${nacos.config.group:DEFAULT_GROUP}
        password: ${nacos.config.password}
        username: ${nacos.config.username}
        # 配置刷新
        refresh-enabled: true
        # 超时配置
        timeout: 10000
      # 服务发现配置
      discovery:
        enabled: true
        server-addr: ${nacos.discovery.address}
        namespace: ${nacos.discovery.namespace}
        group: ${nacos.discovery.group:DEFAULT_GROUP}
        password: ${nacos.discovery.password}
        username: ${nacos.discovery.username}
        # 心跳间隔和超时设置
        heart-beat-interval: 5000
        heart-beat-timeout: 15000
        # 实例注册超时
        register-enabled: true
      # 全局超时配置
      timeout: 10000
      # 如果gRPC连接有问题，可以尝试禁用gRPC使用HTTP
      # config:
      #   remote-first: false
      # discovery:
      #   remote-first: false

# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: S83JC73HY82HW8237CH3Y7HY
    # 令牌有效期（默认30分钟）
    expireTime: 3000
  
# MyBatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.tunnel.**.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath*:mapper/**/*Mapper.xml
    # 加载全局的配置文件
    configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper: 
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql 

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss: 
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

mqtt:
  ssl:
    subscribe-topic: 'test/topic/#'

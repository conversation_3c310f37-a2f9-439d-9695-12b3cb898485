package com.tunnel.biz.strategy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tunnel.biz.ReportStrategy;
import com.tunnel.biz.dto.QueryReportDTO;
import com.tunnel.domain.dto.AuditRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 2025年07月13日 11:15
 * 边坡信息上报
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SlopeReportStrategy implements ReportStrategy {

    @Resource
    private final ObjectMapper objectMapper;

    //@Resource
//    private CheckSlopeService checkSlopeService;
//
//    @Resource
//    private RepairRecordService repairRecordService;

    @Override
    public Boolean create(Object data) {
        try {
//            CheckSlope checkSlope = objectMapper.convertValue(data, CheckSlope.class);
//            int record = checkSlopeService.insertCheckSlope(checkSlope);
//            return record > 0;
            return true;
        } catch (Exception e) {
            log.error("边坡信息上报新增失败：{}", e.getMessage());
            return false;
        }
    }

    @Override
    public Boolean update(Long id, Object data) {
        try {
//            CheckSlope checkSlope = objectMapper.convertValue(data, CheckSlope.class);
//            checkSlope.setId(id);
//            //查询当前记录
//            CheckSlope slope = checkSlopeService.selectCheckSlopeById(id);
//            if(Objects.isNull(slope)){
//                throw new ServiceException("检测信息不存在");
//            }
//            if(Objects.equals(slope.getCheckStatus(), 2)){
//                checkSlope.setCheckStatus(0);
//                checkSlope.setRemark("");
//            }
//            int record = checkSlopeService.updateCheckSlope(checkSlope);
//            return record > 0;
            return true;
        } catch (Exception e) {
            log.error("边坡信息上报修改失败：{}", e.getMessage());
            return false;
        }
    }

    @Override
    public Boolean delete(Long id) {
//        return checkSlopeService.deleteCheckSlopeById(id) > 0;
        return true;
    }

    @Override
    public List<Object> query(QueryReportDTO queryReportDTO) {
//        CheckSlope checkSlope = new CheckSlope();
//        checkSlope.setCompanyName(queryReportDTO.getCompanyName());
//        checkSlope.setRoadCodeOrName(queryReportDTO.getRoadCodeOrName());
//        checkSlope.setCheckStatus(queryReportDTO.getCheckStatus());
//        checkSlope.setRiskLevel(queryReportDTO.getRiskLevel());
//        List<CheckSlope> list = checkSlopeService.selectCheckSlopeList(checkSlope);
//        return new ArrayList<>(list);
        return new ArrayList<>();
    }

    @Override
    public List<Object> queryRepair(QueryReportDTO queryReportDTO) {
//        CheckSlope checkSlope = new CheckSlope();
//        checkSlope.setCompanyName(queryReportDTO.getCompanyName());
//        checkSlope.setRoadCodeOrName(queryReportDTO.getRoadCodeOrName());
//        List<SlopeRepairDTO> list = repairRecordService.selectCheckSlopeWithRepairList(checkSlope);
//        return new ArrayList<>(list);
        return new ArrayList<>();
    }

    @Override
    public void audit(AuditRequest auditRequest) {
        //checkSlopeService.auditCheckSlope(auditRequest);
    }
}

package com.tunnel.biz.strategy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tunnel.biz.ReportStrategy;
import com.tunnel.biz.dto.QueryReportDTO;
import com.tunnel.domain.dto.AuditRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 2025年07月13日 11:15
 * 隧道信息上报
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TunnelReportStrategy implements ReportStrategy {

    @Resource
    private final ObjectMapper objectMapper;

//    @Resource
//    private CheckTunnelService checkTunnelService;
//
//    @Resource
//    private RepairRecordService repairRecordService;

    @Override
    public Boolean create(Object data) {
        try {
//            CheckTunnel checkTunnel = objectMapper.convertValue(data, CheckTunnel.class);
//            int record = checkTunnelService.insertCheckTunnel(checkTunnel);
//            return record > 0;
            return true;
        } catch (Exception e) {
            log.error("隧道信息上报新增失败：{}", e.getMessage());
            return false;
        }
    }

    @Override
    public Boolean update(Long id, Object data) {
        try {
//            CheckTunnel checkTunnel = objectMapper.convertValue(data, CheckTunnel.class);
//            checkTunnel.setId(id);
//            //查询当前记录
//            CheckTunnel tunnel = checkTunnelService.selectCheckTunnelById(id);
//            if(Objects.isNull(tunnel)){
//                throw new ServiceException("检测信息不存在");
//            }
//            if(Objects.equals(tunnel.getCheckStatus(), 2)){
//                checkTunnel.setCheckStatus(0);
//                checkTunnel.setRemark("");
//            }
//            int record = checkTunnelService.updateCheckTunnel(checkTunnel);
//            return record > 0;
            return true;
        } catch (Exception e) {
            log.error("隧道信息上报修改失败：{}", e.getMessage());
            return false;
        }
    }

    @Override
    public Boolean delete(Long id) {
        // return checkTunnelService.deleteCheckTunnelById(id) > 0;
        return true;
    }

    @Override
    public List<Object> query(QueryReportDTO queryReportDTO) {
//        CheckTunnel checkTunnel = new CheckTunnel();
//        checkTunnel.setCompanyName(queryReportDTO.getCompanyName());
//        checkTunnel.setRoadCodeOrName(queryReportDTO.getRoadCodeOrName());
//        checkTunnel.setCheckStatus(queryReportDTO.getCheckStatus());
//        checkTunnel.setRiskLevel(queryReportDTO.getRiskLevel());
//        List<CheckTunnel> list = checkTunnelService.selectCheckTunnelList(checkTunnel);
//        return new ArrayList<>(list);
        return new ArrayList<>();
    }

    @Override
    public List<Object> queryRepair(QueryReportDTO queryReportDTO) {
//        CheckTunnel checkTunnel = new CheckTunnel();
//        checkTunnel.setCompanyName(queryReportDTO.getCompanyName());
//        checkTunnel.setRoadCodeOrName(queryReportDTO.getRoadCodeOrName());
//        List<TunnelRepairDTO> list = repairRecordService.selectCheckTunnelWithRepairList(checkTunnel);
//        return new ArrayList<>(list);
        return new ArrayList<>();
    }

    @Override
    public void audit(AuditRequest auditRequest) {
//        checkTunnelService.auditCheckTunnel(auditRequest);
    }
}

package com.tunnel.biz.dto;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;


/**
 * 查询已经上报信息
 *
 * <AUTHOR>
 * 2025年07月13日 15:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryReportDTO extends BaseEntity {
    /**
     * 上报类型
     * 0: 桥梁
     * 1: 隧道
     * 2: 涵洞
     * 3: 边坡
     * 4: 路面
     */
    @NotNull(message = "类型不能为空")
    private Integer type;

    /**
     * 路线编码或名称
     */
    private String roadCodeOrName;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 风险等级
     */
    private String riskLevel;

    /**
     * 0=待审核,1=审核通过,2=不通过
     */
    private Integer checkStatus;
}

package com.tunnel.framework.event;

import com.tunnel.framework.config.MqttSSLConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/5  17:33
 * @since 1.0.0
 */
@Slf4j
@Component
public class RefreshMqttTopic implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Resource
    private MqttSSLConfig mqttSSLConfig;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 刷新MQTT的消息接收器的订阅
     */
    public void refreshMqttPahoMessageDrivenChannelAdapter() {
        refreshMqttPahoMessageDrivenChannelAdapter(null, null);
    }



    /**
     * 刷新MQTT的消息接收器的订阅
     */
    public void refreshMqttPahoMessageDrivenChannelAdapter(List<String> needAddTopics, List<String> needDelTopics) {
        MqttPahoMessageDrivenChannelAdapter adapter = this.applicationContext.getBean(MqttPahoMessageDrivenChannelAdapter.class);
        String[] alreadyExistTopics = adapter.getTopic();
        List<String> addTopicList = new ArrayList<>();
        List<String> removeTopicList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(needAddTopics)) {
            addTopicList.addAll(needAddTopics);
        }
        if (CollectionUtils.isNotEmpty(needDelTopics)) {
            removeTopicList.addAll(needDelTopics);
        }
        if (CollectionUtils.isEmpty(needAddTopics) && CollectionUtils.isEmpty(needDelTopics)) {
            String[] configTopics = mqttSSLConfig.getConfigTopic();
            for (String topic : alreadyExistTopics) {
                if (!ArrayUtils.contains(configTopics, topic)) {
                    removeTopicList.add(topic);
                }
            }
            for (String topic : configTopics) {
                if (!ArrayUtils.contains(alreadyExistTopics, topic)) {
                    addTopicList.add(topic);
                }
            }
        }
        log.info("MqttTopicRefreshTask.refreshMqttTopic is : {}", adapter.isActive());
        if (CollectionUtils.isNotEmpty(addTopicList)) {
            String[] addTopicArray = new String[addTopicList.size()];
            adapter.addTopic(addTopicList.toArray(addTopicArray));
            log.info("MqttTopicRefresh addTopic: {}", addTopicList);
        }
        if (CollectionUtils.isNotEmpty(removeTopicList)) {
            String[] removeTopicArray = new String[removeTopicList.size()];
            adapter.removeTopic(removeTopicList.toArray(removeTopicArray));
            log.info("MqttTopicRefresh del Topic: {}", removeTopicList);
        }
        adapter.start();
    }
}

package com.tunnel.service.impl;

import com.tunnel.framework.event.RefreshMqttTopic;
import com.tunnel.service.RequirementRefreshService;
import com.tunnel.service.model.RequirementRefreshModel;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/8/5  18:04
 * @since 1.0.0
 */
@Service
public class RequirementRefreshServiceImpl implements RequirementRefreshService {

    @Resource
    private RefreshMqttTopic refreshMqttTopic;

    @Override
    public void refresh(RequirementRefreshModel requirementRefreshModel) {
        refreshMqttTopic.refreshMqttPahoMessageDrivenChannelAdapter(
                requirementRefreshModel.getAddTopics(),
                requirementRefreshModel.getDelTopics());
    }
}

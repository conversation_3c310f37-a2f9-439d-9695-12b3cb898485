package com.tunnel.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.service.RequirementRefreshService;
import com.tunnel.service.model.RequirementRefreshModel;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 设备订阅的刷新服务
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/requirement/refresh")
public class RequirementRefreshController {
    @Resource
    private RequirementRefreshService requirementRefreshService;

    /**
     * 刷新订阅
     *
     * @param model 刷新参数
     */
    @PostMapping("")
    public AjaxResult refresh(@RequestBody RequirementRefreshModel model) {
        requirementRefreshService.refresh(model);
        return AjaxResult.success();
    }
}

package com.tunnel.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.service.WxService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class WxCallbackController {

    @Resource
    private WxService wxService;

    @ApiOperation("微信消息配置验证")
    @GetMapping("/wx/callback")
    @Anonymous
    public String wxServerCallback(
            @RequestParam("signature") String signature,
            @RequestParam("timestamp") String timestamp,
            @RequestParam("nonce") String nonce,
            @RequestParam("echostr") String echostr
    ) {
        //消息合法
        if (wxService.checkSignature(signature, timestamp, nonce)) {
            return echostr;
        }
        return null;
    }
}
package com.tunnel.controller;

import com.tunnel.biz.ReportStrategy;
import com.tunnel.biz.dto.QueryReportDTO;
import com.tunnel.biz.dto.ReportDTO;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.domain.Road;
import com.tunnel.domain.dto.AuditRequest;
import com.tunnel.factory.ReportStrategyFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 通用接口
 *
 * <AUTHOR>
 * 2025年07月13日 11:29
 */
@Slf4j
@RestController
@RequestMapping("/common/report")
@RequiredArgsConstructor
@Api(tags = "通用上报接口管理")
public class CommonController extends BaseController {

    private final ReportStrategyFactory strategyFactory;

    /**
     * 处理道路信息上报
     *
     * @param reportDTO 上报请求参数
     * @return 处理结果
     */
    @PostMapping("/insertOrUpdate")
    @ApiOperation(value = "道路信息上报", notes = "处理各类道路设施的信息上报，包括新增和修改")
    @Operation(summary = "道路信息上报", description = "处理各类道路设施的信息上报，包括新增和修改")
    public AjaxResult handleReport(@RequestBody ReportDTO reportDTO) {
        ReportStrategy strategy = strategyFactory.getStrategy(reportDTO.getType());
        if (strategy == null) {
            return error("道路信息上报: 不支持的上报类型：" + reportDTO.getType());
        }
        try {
            Boolean isSuccess;
            if (reportDTO.getId() == null) {
                isSuccess = strategy.create(reportDTO.getData());
            } else {
                isSuccess = strategy.update(reportDTO.getId(), reportDTO.getData());
            }
            return success(isSuccess);
        } catch (Exception e) {
            return error("处理失败：" + e.getMessage());
        }
    }

    /**
     * 删除道路信息上报记录
     *
     * @param id 记录ID
     * @param type 上报类型
     * @return 处理结果
     */
    @GetMapping("/delete/{id}")
    @ApiOperation(value = "删除道路信息上报", notes = "删除指定的道路设施信息上报记录")
    @Operation(summary = "删除道路信息上报", description = "删除指定的道路设施信息上报记录")
    public AjaxResult handleDelete(@PathVariable("id") Long id, @RequestParam("type") Integer type) {
        ReportStrategy strategy = strategyFactory.getStrategy(type);
        if (strategy == null) {
            return error("删除道路信息上报: 不支持的上报类型：" + type);
        }
        try {
            Boolean isSuccess = strategy.delete(id);
            return success(isSuccess);
        } catch (Exception e) {
            return error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取已上报信息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取已上报信息列表", notes = "获取已上报信息列表")
    @Operation(summary = "获取已上报信息列表", description = "获取已上报信息列表")
    public TableDataInfo list(@Valid QueryReportDTO queryReportDTO) {
        ReportStrategy strategy = strategyFactory.getStrategy(queryReportDTO.getType());
        if (strategy == null) {
            log.error("获取已上报信息列表: 不支持的上报类型：{}", queryReportDTO.getType());
            return getDataTable(new ArrayList<>());
        }
        startPage();
        List<Object> list = strategy.query(queryReportDTO);
        return getDataTable(list);
    }

    /**
     * 获取已上报维修信息列表
     */
    @GetMapping("/repairList")
    @ApiOperation(value = "获取已上报维修信息列表", notes = "获取已上报维修信息列表")
    @Operation(summary = "获取已上报维修信息列表", description = "获取已上报维修信息列表")
    public TableDataInfo repairList(@Valid QueryReportDTO queryReportDTO) {
        ReportStrategy strategy = strategyFactory.getStrategy(queryReportDTO.getType());
        if (strategy == null) {
            log.error("获取已上报维修信息列表: 不支持的上报类型：{}", queryReportDTO.getType());
            return getDataTable(new ArrayList<>());
        }
        startPage();
        List<Object> list = strategy.queryRepair(queryReportDTO);
        return getDataTable(list);
    }



    @PostMapping("/audit")
    @ApiOperation(value = "审核", notes = "审核")
    @Operation(summary = "审核", description = "审核")
    public AjaxResult audit(@RequestBody AuditRequest dto) {
        if(Objects.isNull(dto)){
            return error("参数错误");
        }
        ReportStrategy strategy = strategyFactory.getStrategy(dto.getType());
        if (strategy == null) {
            return error("道路信息上报: 不支持的上报类型：" + dto.getType());
        }
        strategy.audit(dto);
        return success();
    }

}

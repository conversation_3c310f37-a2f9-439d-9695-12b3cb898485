package com.tunnel.factory;

import com.tunnel.biz.ReportStrategy;
import com.tunnel.biz.strategy.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 上报策略工厂类
 * 负责管理和分发不同类型的上报策略实现
 * <AUTHOR>
 * 2025年07月13日 11:09
 */

@Slf4j
@Component
public class ReportStrategyFactory {
    /**
     * 策略映射表，key为上报类型，value为对应的策略实现
     * 0: 桥梁
     * 1: 隧道
     * 2: 涵洞
     * 3: 边坡
     * 4: 路面
     */
    private final Map<Integer, ReportStrategy> strategyMap = new HashMap<>();

    /**
     * 构造函数，初始化策略映射
     * @param strategies 所有策略实现的列表，通过Spring自动注入
     */
    public ReportStrategyFactory(List<ReportStrategy> strategies) {
        log.info("初始化上报策略工厂，可用策略数量: {}", strategies.size());
        strategyMap.put(1, findStrategy(TunnelReportStrategy.class, strategies));
        strategyMap.put(2, findStrategy(HoleReportStrategy.class, strategies));
        strategyMap.put(3, findStrategy(SlopeReportStrategy.class, strategies));
        strategyMap.put(4, findStrategy(RoadReportStrategy.class, strategies));
        log.info("上报策略工厂初始化完成，已注册策略类型: {}", strategyMap.keySet());
    }

    /**
     * 根据上报类型获取对应的策略实现
     * @param type 上报类型
     * @return 对应的策略实现，如果类型不存在则返回null
     */
    public ReportStrategy getStrategy(Integer type) {
        ReportStrategy strategy = strategyMap.get(type);
        if (strategy == null) {
            log.warn("未找到类型[{}]对应的上报策略", type);
        } else {
            log.debug("获取类型[{}]的上报策略: {}", type, strategy.getClass().getSimpleName());
        }
        return strategy;
    }

    /**
     * 从策略列表中查找指定类型的策略实现
     * @param strategyClass 策略类型
     * @param strategies 策略列表
     * @return 找到的策略实现
     * @throws IllegalArgumentException 如果未找到指定类型的策略实现
     */
    private ReportStrategy findStrategy(Class<?> strategyClass, List<ReportStrategy> strategies) {
        return strategies.stream().filter(strategyClass::isInstance).findFirst()
                .orElseThrow(() -> {
                    String errorMsg = String.format("未找到策略实现: %s", strategyClass.getSimpleName());
                    log.error(errorMsg);
                    return new IllegalArgumentException(errorMsg);
                });
    }
}

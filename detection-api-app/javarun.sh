#!/bin/bash
#项目根目录
PROJECT_NAME=detection-admin
#App名称
APP_NAME=detection-api-app
#运行端口
PORT=8108
#运行包名称
JAR_NAME=${APP_NAME}.jar
# 镜像版本
IMAGE_TAG="latest"
#镜像名称
IMAGE_NAME=${APP_NAME}

echo "开始切换到master分支,执行git pull"
cd /data/codes/${PROJECT_NAME}
git checkout master
git pull
echo ">>>>> 正在打包 Java 项目..."
mvn clean package -DskipTests
cd ./${APP_NAME}
# 检查打包是否成功
if [ ! -f "target/$JAR_NAME" ]; then
  echo ">>>>> 打包失败，无法找到 $JAR_NAME 文件！"
  exit 1
fi

# 构建 Docker 镜像
echo ">>>>> 正在构建 Docker 镜像..."
docker build -t $IMAGE_NAME:$IMAGE_TAG .

# 停止旧容器（如果存在）
if [ "$(docker ps -q -f name=$IMAGE_NAME)" ]; then
  echo ">>>>> 停止旧容器..."
  docker stop $IMAGE_NAME
fi

# 删除旧容器（如果存在）
if [ "$(docker ps -aq -f name=$IMAGE_NAME)" ]; then
  echo ">>>>> 删除旧容器..."
  docker rm $IMAGE_NAME
fi

# 运行新的容器
echo ">>>>> 正在运行新的容器..."
docker run -d --name $IMAGE_NAME --restart=always --memory="2g" -e TZ=Asia/Shanghai -p $PORT:$PORT $IMAGE_NAME:$IMAGE_TAG

# 检查容器状态
echo ">>>>> 容器运行状态："
docker ps -f name=$IMAGE_NAME
-- 设备状态记录表
create table sc_requirement_status (
                                       id bigint unsigned auto_increment comment '唯一主键' primary key,
                                       `b_key` varchar(255) not null comment '业务标识key',
                                       `topic` varchar(255) null default null comment '路由',
                                       `vb100` varchar(255) null default null comment 'F01(细格栅状态)',
                                       `vb101` varchar(255) null default null comment 'P02(砂滤泵)',
                                       `vb102` varchar(255) null default null comment 'P03(反洗泵)',
                                       `vb103` varchar(255) null default null comment 'B01(工艺风机)',
                                       `vb104` varchar(255) null default null comment 'B03(混合风机)',
                                       `vb105` varchar(255) null default null comment 'SC01(刮泥机)',
                                       `vb106` varchar(255) null default null comment 'P01(污泥泵)',
                                       `vb107` varchar(255) null default null comment 'DP01(碳源泵)',
                                       `vb108` varchar(255) null default null comment 'DP02(混凝剂泵)',
                                       `vb109` varchar(255) null default null comment 'DP03(次钠泵)',
                                       `vb110` varchar(255) null default null comment 'C_01(空压机)',
                                       `vb111` varchar(255) null default null comment 'P00A(进水泵A)',
                                       `vb112` varchar(255) null default null comment 'P00B(进水泵B)',
                                       `vb113` varchar(255) null default null comment 'B02(曝气风机)',
                                       `vb114` varchar(255) null default null comment 'F00(粗格栅)',
                                       `vb115` varchar(255) null default null comment 'XV201A(1#膜池混合阀A)',
                                       `vb116` varchar(255) null default null comment 'XV201B(1#膜池混合阀B)',
                                       `vb117` varchar(255) null default null comment 'XV201C(1#膜池混合阀C)',
                                       `vb118` varchar(255) null default null comment 'XV201D(1#膜池混合阀D)',
                                       `vb119` varchar(255) null default null comment 'XV202A(2#膜池混合阀A)',
                                       `vb120` varchar(255) null default null comment 'XV202B(2#膜池混合阀B)',
                                       `vb121` varchar(255) null default null comment 'XV202C(2#膜池混合阀C)',
                                       `vb122` varchar(255) null default null comment 'XV202D(2#膜池混合阀D)',
                                       `vb123` varchar(255) null default null comment 'XV203(回流阀)',
                                       `vb124` varchar(255) null default null comment 'XV204(排泥阀)',
                                       `vb125` varchar(255) null default null comment 'XV301(砂滤进水阀)',
                                       `vb126` varchar(255) null default null comment 'XV302(砂滤进水阀)',
                                       `vb127` varchar(255) null default null comment 'XV303(砂滤反洗阀)',
                                       `vb128` varchar(255) null default null comment 'XV304(砂滤反洗阀)',
                                       `vb129` varchar(255) null default null comment 'XV101(细格栅冲洗阀)',
                                       `vb130` varchar(255) null default null comment 'XV401(储泥池气动阀)',
                                       `vb131` varchar(255) null default null comment '备用1',
                                       `remark` varchar(255) null default null comment '备注',
                                       `create_time` datetime not null default current_timestamp comment '创建时间',
                                       `update_time` datetime not null default current_timestamp comment '更新时间',
                                       `creator` bigint(20) null default null comment '创建人',
                                       `modifier` bigint(20) null default null comment '更新人'
) comment '设备状态信息表' collate = utf8mb4_general_ci;
-- 设备预警表
create table sc_requirement_warning (
                                       id bigint unsigned auto_increment comment '唯一主键' primary key,
                                       `b_key` varchar(255) not null comment '业务标识key',
                                       `topic` varchar(255) null default null comment '路由',
                                       `v91_0` varchar(255) null default null comment '调节池液位低',
                                       `v91_1` varchar(255) null default null comment 'F-01 溢流(细格栅高液位)',
                                       `v91_2` varchar(255) null default null comment 'FT201 流量低(污泥流量计)',
                                       `v91_3` varchar(255) null default null comment 'T-02(二级产水池)溢流(二级产水池)',
                                       `v91_4` varchar(255) null default null comment 'T-03液位过低(三级产水池)',
                                       `v91_5` varchar(255) null default null comment 'FIT101流量过低',
                                       `v92_0` varchar(255) null default null comment 'F01故障(细格栅)',
                                       `v92_1` varchar(255) null default null comment 'P02故障(砂滤进水泵)',
                                       `v92_2` varchar(255) null default null comment 'P03故障(砂滤反洗泵)',
                                       `v92_3` varchar(255) null default null comment 'B01故障(工艺风机)',
                                       `v92_4` varchar(255) null default null comment 'B03故障(曝气风机)',
                                       `v92_5` varchar(255) null default null comment 'SC01故障(刮泥机)',
                                       `v92_6` varchar(255) null default null comment 'P01故障(污泥泵)',
                                       `v92_7` varchar(255) null default null comment 'DP01故障(碳源泵)',
                                       `v93_0` varchar(255) null default null comment 'DP02故障(混凝剂泵)',
                                       `v93_1` varchar(255) null default null comment 'DP03故障(次钠泵)',
                                       `v93_2` varchar(255) null default null comment 'C01故障(空压机)',
                                       `v93_3` varchar(255) null default null comment 'P00A故障(进水泵A)',
                                       `v93_4` varchar(255) null default null comment 'P00B故障(进水泵B)',
                                       `v93_5` varchar(255) null default null comment 'B02故障(混合风机)',
                                       `v93_6` varchar(255) null default null comment 'F00故障(粗格栅)',
                                       `v93_7` varchar(255) null default null comment '急停故障',
                                       `v95_0` varchar(255) null default null comment 'LS 401液位报警(储泥池液位报警)',
                                       `v95_1` varchar(255) null default null comment '液位开关错误(LS301,302位置错误)',
                                       `v95_2` varchar(255) null default null comment '液位开关错误(LS301,303位置错误)',
                                       `v95_3` varchar(255) null default null comment '液位开关错误(LS302,303位置错误)',
                                       `v91_6` varchar(255) null default null comment '反洗提示',
                                       `v91_7` varchar(255) null default null comment '无气压',
                                       `v95_4` varchar(255) null default null comment '碳源药罐液位低',
                                       `v95_5` varchar(255) null default null comment '混凝剂药罐液位低',
                                       `v95_6` varchar(255) null default null comment '消毒药罐液位低',
                                       `remark` varchar(255) null default null comment '备注',
                                       `create_time` datetime not null default current_timestamp comment '创建时间',
                                       `update_time` datetime not null default current_timestamp comment '更新时间',
                                       `creator` bigint(20) null default null comment '创建人',
                                       `modifier` bigint(20) null default null comment '更新人'
) comment '设备报警信息表' collate = utf8mb4_general_ci;
-- 设备上报数据表----
create table sc_requirement_report (
                                       id bigint unsigned auto_increment comment '唯一主键' primary key,
                                       `b_key` varchar(255) not null comment '业务标识key',
                                       `topic` varchar(255) null default null comment '路由',
                                       `i20_0` varchar(255) null default null comment 'LS301_H(二级产水池高)',
                                       `i20_1` varchar(255) null default null comment 'LS302_L(二级产水池中)',
                                       `i20_2` varchar(255) null default null comment 'LS303_LL(二级产水池低)',
                                       `i20_3` varchar(255) null default null comment 'LS304(三级产水池低)',
                                       `i20_4` varchar(255) null default null comment 'LS001(原水池高)',
                                       `i20_5` varchar(255) null default null comment 'LS002_L(原水池中)',
                                       `i20_6` varchar(255) null default null comment 'LS003_LL(原水池低)',
                                       `i21_0` varchar(255) null default null comment 'PSA_601(空压机无气压)',
                                       `i21_1` varchar(255) null default null comment 'LS101_H(细格栅高位)',
                                       `i21_2` varchar(255) null default null comment 'LS501_LL(碳源低液位)',
                                       `i21_3` varchar(255) null default null comment 'LS502_LL(次钠低液位)',
                                       `i21_4` varchar(255) null default null comment 'LS503_LL(消毒剂低液位)',
                                       `vd200` varchar(255) null default null comment 'FIT101(进水流量计)',
                                       `vd230` varchar(255) null default null comment 'FIT201(回流/污泥流量计)',
                                       `vd260` varchar(255) null default null comment 'DPT301(压差计)',
                                       `vd2068` varchar(255) null default null comment 'TT201(温度计)',
                                       `vd404` varchar(255) null default null comment 'FT101_Total(FIT101总流量)',
                                       `vd408` varchar(255) null default null comment 'FT201_Total(FIT201总流量)',
                                       `vd704` varchar(255) null default null comment 'FIT101PD(FIT101每天数据)',
                                       `vd712` varchar(255) null default null comment 'FIT101PM(FIT101每月数据)',
                                       `vd716` varchar(255) null default null comment 'FIT101PY(FIT101每年数据)',
                                       `vd720` varchar(255) null default null comment 'FIT201RAS_TOTAL(FIT201回流总流量)',
                                       `vd728` varchar(255) null default null comment 'FIT201RASPD(FIT201回流每天数据)',
                                       `vd736` varchar(255) null default null comment 'FIT201RASPM(FIT201回流每月数据)',
                                       `vd740` varchar(255) null default null comment 'FIT201RASPY(FIT201回流每年数据)',
                                       `vd744` varchar(255) null default null comment 'FIT201WAS_TOTAL(FIT201排泥总流量)',
                                       `vd752` varchar(255) null default null comment 'FIT201WASPD(FIT201排泥每天数据)',
                                       `vd760` varchar(255) null default null comment 'FIT201WASPM(FIT201排泥每月数据)',
                                       `vd764` varchar(255) null default null comment 'FIT201WASPY(FIT201排泥每年数据)',
                                       `vd904` varchar(255) null default null comment 'FIT101PD前一(FIT101前一周期每天数据)',
                                       `vd912` varchar(255) null default null comment 'FIT101PM前一(FIT101前一周期每月数据)',
                                       `vd916` varchar(255) null default null comment 'FIT101PY前一(FIT101前一周期每年数据)',
                                       `vd928` varchar(255) null default null comment 'FIT201RASPD前一(FIT201前一周期每天回流数据)',
                                       `vd936` varchar(255) null default null comment 'FIT201RASPM前一(FIT201前一周期每月回流数据)',
                                       `vd940` varchar(255) null default null comment 'FIT201RASPY前一(FIT201前一周期每年回流数据)',
                                       `vd952` varchar(255) null default null comment 'FIT201WASPD前一(FIT201前一周期每天排泥数据)',
                                       `vd960` varchar(255) null default null comment 'FIT201WASPM前一(FIT201前一周期每月排泥回流数据)',
                                       `vd964` varchar(255) null default null comment 'FIT201WASPY前一(FIT201前一周期每年排泥回流数据)',
                                       `remark` varchar(255) null default null comment '备注',
                                       `create_time` datetime not null default current_timestamp comment '创建时间',
                                       `update_time` datetime not null default current_timestamp comment '更新时间',
                                       `creator` bigint(20) null default null comment '创建人',
                                       `modifier` bigint(20) null default null comment '更新人'
) comment '设备上报数据信息表' collate = utf8mb4_general_ci;
-- 设备表
create table sc_requirement (
                                id bigint unsigned auto_increment comment '唯一主键' primary key,
                                `code` varchar(255)  null default null comment '设备编码',
                                `name` varchar(255) null default null comment '设备名称',
                                `type` varchar(255) null default null comment '设备类型',
                                `type_name` varchar(255) null default null comment '设备类型',
                                `mac_address` varchar(255) null default null comment 'mac地址',
                                `ip` varchar(255) null default null comment 'ip',
                                `road_id` bigint(255) not null comment '路线id',
                                `data_from` varchar(255) null default null comment '数据来源，mqtt，',
                                `topic` varchar(255) null default null comment '消息方式的topic',
                                `remark` varchar(255) null default null comment '备注',
                                `create_time` datetime not null default current_timestamp comment '创建时间',
                                `update_time` datetime not null default current_timestamp comment '更新时间',
                                `creator` bigint(20) null default null comment '创建人',
                                `modifier` bigint(20) null default null comment '更新人'
) comment '设备信息表' collate = utf8mb4_general_ci;

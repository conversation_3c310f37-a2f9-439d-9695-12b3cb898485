-- 一期的表
-- 监测类型
create table sc_monitor_type(
    id             bigint(20)  auto_increment  not null primary key,
    code           varchar(255)   not null comment '监测类型编码',
    name           varchar(255)   null default null comment '监测类型名称',
    `remark` varchar(255) null default null comment '备注',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    `update_time` datetime not null default current_timestamp comment '更新时间',
    `creator` bigint(20) null default null comment '创建人',
    `modifier` bigint(20) null default null comment '更新人',
    constraint code unique (code)
) comment '监测类型表' collate = utf8mb4_general_ci;

-- 增加监测类型
insert into monitor_type(code, name) values('sewage', '污水类');
insert into monitor_type(code, name) values('air', '大气类');
insert into monitor_type(code, name) values('noise', '噪音类');
insert into monitor_type(code, name) values('solid', '固废类');


-- 监测站点
create table sc_monitor_station(
    id             bigint(20)  auto_increment  not null primary key,
    type           varchar(20)    not null comment '类型',
    code           varchar(255)   not null comment '站点编码',
    city           varchar(255)   null default null comment '城市',
    district       varchar(255)   null default null comment '地区',
    lat            decimal(19, 6) null default null comment '经度',
    lon            decimal(19, 6) null default null comment '纬度',
    name           varchar(255)   null default null comment '站点名称',
    province       varchar(255)   null default null comment '省份',
    state          tinyint(1)     null default null comment '',
    pollution_type varchar(255)   null default null comment '污染类型',
    faulty         tinyint(1)     null default null comment '',
    stationIndex   tinyint(1)     null default null comment '站点顺序',
    `remark` varchar(255) null default null comment '备注',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    `update_time` datetime not null default current_timestamp comment '更新时间',
    `creator` bigint(20) null default null comment '创建人',
    `modifier` bigint(20) null default null comment '更新人',
    constraint code unique (code)
) comment '监测站点表' collate = utf8mb4_general_ci;

-- 监测站点和类型关联表
create table sc_monitor_station_relation(
    id             bigint(20)  auto_increment  not null primary key,
    code           varchar(255)   not null comment '站点编码',
    monitor_type   varchar(255)   not null comment '监测类型',
    monitor_type_name   varchar(255)   not null comment '监测类型名称',
    `remark` varchar(255) null default null comment '备注',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    `update_time` datetime not null default current_timestamp comment '更新时间',
    `creator` bigint(20) null default null comment '创建人',
    `modifier` bigint(20) null default null comment '更新人'
) comment '监测站点和类型关联表' collate = utf8mb4_general_ci;

-- 监测站点信息
create table sc_monitor_station_relation_info(
      id             bigint(20)  auto_increment  not null primary key,
      code           varchar(255)   not null comment '站点编码',
      monitor_type   varchar(255)   not null comment '监测类型',
      monitor_type_name   varchar(255)   not null comment '监测类型名称',
      design_stands   varchar(1000)    null  comment '设计标准,多个之间以‘;’分割',
      eia_stands      varchar(1000) null comment '环评标准,多个之间以‘;’分割',
      pic            longblob     null comment '图片',
      pollutions     varchar(1000) null comment '污染源类型,多个之间以‘;’分割',
      `remark` varchar(255) null default null comment '备注',
      `create_time` datetime not null default current_timestamp comment '创建时间',
      `update_time` datetime not null default current_timestamp comment '更新时间',
      `creator` bigint(20) null default null comment '创建人',
      `modifier` bigint(20) null default null comment '更新人',
      constraint code unique (code)
) comment '监测站点详细信息' collate = utf8mb4_general_ci;

-- 监测站点和用户关联表
create table sc_monitor_station_user_rel(
    id             bigint(20)  auto_increment  not null primary key,
    service_code           varchar(20)    not null comment '类型',
    user_name           varchar(255)   not null comment '用户名',
    `remark` varchar(255) null default null comment '备注',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    `update_time` datetime not null default current_timestamp comment '更新时间',
    `creator` bigint(20) null default null comment '创建人',
    `modifier` bigint(20) null default null comment '更新人'
) comment '监测站点和用户关联表' collate = utf8mb4_general_ci;

-- 系统详细信息
create table sc_integration_system(
   id              bigint(20) auto_increment not null primary key,
   type            varchar(20)  not null comment '类型,是否接入系统',
   system_code      varchar(255) not null comment '系统编码',
   monitor_type    varchar(255) not null comment '监测类型编码',
   monitor_type_name   varchar(255)   not null comment '监测类型名称',
   `name`            varchar(255) null comment '名称',
   monitor_station_code varchar(255) not null comment '关联表monitor_station',
   `remark` varchar(255) null default null comment '备注',
   `create_time` datetime not null default current_timestamp comment '创建时间',
   `update_time` datetime not null default current_timestamp comment '更新时间',
   `creator` bigint(20) null default null comment '创建人',
   `modifier` bigint(20) null default null comment '更新人',
   constraint code unique (system_code)
) comment '系统详细信息' collate = utf8mb4_general_ci;

-- 监测因子
create table sc_monitor_factor(
    id                          bigint(20)  auto_increment not null primary key,
    code                        varchar(255)   not null comment '监测因子编码',
    max_value                   decimal(19, 2) null comment '最大值',
    min_value                   decimal(19, 2) null comment '最小值',
    name                        varchar(255)   null comment '监测因子名称',
    sensor_id                   varchar(255)   null default null comment '设备ID',
    unit                        varchar(255)   null comment '单位',
    monitoring_system_system_code varchar(255)   null comment '系统编码',
    exceeded                    tinyint(1)            not null,
    faulty                      tinyint(1)            not null,
    factor_index                tinyint(1)            null comment '因子排序',
    `remark` varchar(255) null default null comment '备注',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    `update_time` datetime not null default current_timestamp comment '更新时间',
    `creator` bigint(20) null default null comment '创建人',
    `modifier` bigint(20) null default null comment '更新人',
    constraint code unique (code)
) comment '监测因子' collate = utf8mb4_general_ci;

-- 远程控制网关
create table sc_processing_gateway(
   id   bigint(20) auto_increment not null primary key,
   `code`        varchar(255) not null comment '远程网关编码',
   mac  varchar(255) null comment 'mac地址',
   name varchar(255) null comment '网关名称',
   `remark` varchar(255) null default null comment '备注',
   `create_time` datetime not null default current_timestamp comment '创建时间',
   `update_time` datetime not null default current_timestamp comment '更新时间',
   `creator` bigint(20) null default null comment '创建人',
   `modifier` bigint(20) null default null comment '更新人',
   constraint code unique (code),
   constraint code unique (mac)
) comment '远程控制网关' collate = utf8mb4_general_ci;

-- 远程网关和用户关联
create table sc_processing_gateway_user_rel
(
    id             bigint(20)  auto_increment  not null primary key,
    `code`        varchar(255) not null comment '远程网关编码',
    mac           varchar(20)    not null comment '网关mac地址',
    user_name           varchar(255)   not null comment '用户名称',
    `remark` varchar(255) null default null comment '备注',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    `update_time` datetime not null default current_timestamp comment '更新时间',
    `creator` bigint(20) null default null comment '创建人',
    `modifier` bigint(20) null default null comment '更新人'
    comment '远程网关和用户关联表'
);

-- 控制点位
create table sc_secomea_field(
    id                   bigint(20) auto_increment not null primary key,
    checked              tinyint(1)   null default 0 comment '是否选中展示',
    point_addr            varchar(255) not null comment '控制点位编码',
    name                 varchar(255) not null comment '控制点位名称',
    monitor_type    varchar(255) not null comment '控制点位类型编码',
    monitor_type_name   varchar(255)   not null comment '控制点位类型名称',
    processing_gateway_code varchar(255) not null comment '关联的远程网关code',
    `remark` varchar(255) null default null comment '备注',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    `update_time` datetime not null default current_timestamp comment '更新时间',
    `creator` bigint(20) null default null comment '创建人',
    `modifier` bigint(20) null default null comment '更新人'
) comment '控制点位表' collate = utf8mb4_general_ci;


-- 监测报警配置
create table sc_monitor_alarm_config(
    id       bigint(20)  auto_increment not null primary key,
    alarm_enabled         tinyint(1)    null default 1 comment '是否启用',
    alarm_limit           decimal(19, 2) null default null comment '预警上限',
    factor_id             varchar(255)   null default null comment '监测因子编码',
    service_id            varchar(255)   null default null comment '服务区编码',
    system_code            varchar(255)   null default null comment '系统编码',
    system_name            varchar(255)   null default null comment '系统名称',
    factor_name           varchar(255)   null default null comment '监测因子名称',
    max                   decimal(19, 2) null default null comment '最大值',
    min                   decimal(19, 2) null default null comment '最小值',
    service_area          varchar(255)   null default null comment '服务区名称',
    name                  varchar(255)   null default null comment '预警名称',
    email_address         varchar(255)   null default null comment '电子邮箱',
    email_enabled         tinyint(1)            null default null comment '是否启用电子邮箱',
    last_time             varchar(255)   null default null comment '上次邮件通知时间',
    message_enabled       tinyint(1)            null default null comment '是否短信提醒',
    phone_num             varchar(255)   null default null comment '手机号',
    time_interval         int            null default null comment '邮件通知时间间隔',
    last_message_time     varchar(255)   null default null comment '上次短信通知时间',
    message_time_interval int            null default null comment '短信通知时间间隔',
    floor_alarm_limit     decimal(19, 2) null,
    pair_alarm            bit            null,
    pollution_type        varchar(255)   null,
    `remark` varchar(255) null default null comment '备注',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    `update_time` datetime not null default current_timestamp comment '更新时间',
    `creator` bigint(20) null default null comment '创建人',
    `modifier` bigint(20) null default null comment '更新人'
) comment '监测预警配置表' collate = utf8mb4_general_ci;

-- 监测指标报警表
create table sc_monitor_alarm_processing(
    id           bigint(20)   not null auto_increment primary key,
    alarm_limit  decimal(19, 2) null default null comment '报警临界值',
    factor_id    varchar(255)   null default null comment '监测指标编码',
    factor_code  varchar(255)   null default null comment '监测指标code',
    factor_name  varchar(255)   null default null comment '监测指标名称',
    handled      tinyint(1)     not null default 0 comment '是否已处理',
    level        varchar(255)   null default null comment '报警级别',
    max          decimal(19, 2) null default null comment '监测指标',
    min          decimal(19, 2) null default null comment '监测指标',
    service_area varchar(255)   null default null comment '服务区名称',
    service_id   varchar(255)   null default null comment '服务区编码',
    system_code   varchar(255)   null default null comment '监测系统编码',
    system_name   varchar(255)   null default null comment '监测系统名称',
    time         varchar(255)   null default null comment '报警时间',
    real_value   decimal(19, 2) null default null comment '监测指标',
    alarm_level  varchar(255)   null default null comment '报警级别',
    value        decimal(19, 4) null default null comment '',
    `remark` varchar(255) null default null comment '备注',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    `update_time` datetime not null default current_timestamp comment '更新时间',
    `creator` bigint(20) null default null comment '创建人',
    `modifier` bigint(20) null default null comment '更新人'
) comment '监测指标报警表' collate = utf8mb4_general_ci;

create table sc_monitor_alarm_devfault(
    id           bigint(20) not null auto_increment primary key,
    factor_id    varchar(255) null default null comment '监测指标id',
    factor_name  varchar(255) null default null comment '监测指标',
    handled      tinyint(1)   not null default 0 comment '是否已处理',
    service_area varchar(255) null default null comment '服务区名称',
    service_id   varchar(255) null default null comment '服务区编码',
    system_code   varchar(255) null default null comment '系统编码',
    system_name   varchar(255) null default null comment '系统名称',
    time         varchar(255) null default null comment '报警时间',
    sensor_id     varchar(255) null default null comment '设备ID',
    factor_code  varchar(255) null default null comment '监测指标编码',
    `remark` varchar(255) null default null comment '备注',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    `update_time` datetime not null default current_timestamp comment '更新时间',
    `creator` bigint(20) null default null comment '创建人',
    `modifier` bigint(20) null default null comment '更新人'
) comment '设备故障报警表' collate = utf8mb4_general_ci;

-- 日报表
create table sc_daily_report (
      id                 bigint(20)  not null auto_increment primary key,
      monitor_type       varchar(255)               null default null comment '监测类型',
      monitor_type_name   varchar(255)   not null comment '监测类型名称',
      city               varchar(255)               not null comment '城市',
      date_time          varchar(255)               not null comment '日期',
      district           varchar(255)               not null comment '地区',
      factor             varchar(255)               not null comment '监测指标',
      factor_code        varchar(255)               not null comment '监测指标编码',
      integration_system varchar(255)               not null comment '监测系统',
      max_value          decimal(19, 2)             null default null comment '最大值',
      min_value          decimal(19, 2)             null default null comment '最小值',
      service_area       varchar(255)               not null comment '服务区',
      transmission_rate  varchar(255)               null default null comment '传输率',
      upload_rate        varchar(255)               null default null comment '上传率',
      system_code        varchar(255)               null default null comment '系统编码',
      service_code       varchar(255)               not null comment '服务区编码',
      discharge_amount   varchar(255) default '0.0' null default null comment '监测指标',
      average_value      decimal(19, 2)             null default null comment '平均值',
      `remark` varchar(255) null default null comment '备注',
      `create_time` datetime not null default current_timestamp comment '创建时间',
      `update_time` datetime not null default current_timestamp comment '更新时间',
      `creator` bigint(20) null default null comment '创建人',
      `modifier` bigint(20) null default null comment '更新人'
) comment '日报' collate = utf8mb4_general_ci;

-- 月报
create table sc_monthly_report (
        id                 bigint(20)  not null auto_increment primary key,
        monitor_type       varchar(255)               null default null comment '监测类型',
        monitor_type_name   varchar(255)   not null comment '监测类型名称',
        city               varchar(255)               not null comment '城市',
        date_time          varchar(255)               not null comment '日期',
        district           varchar(255)               not null comment '地区',
        factor             varchar(255)               not null comment '监测指标',
        factor_code        varchar(255)               not null comment '监测指标编码',
        integration_system varchar(255)               not null comment '监测系统',
        max_value          decimal(19, 2)             null default null comment '最大值',
        min_value          decimal(19, 2)             null default null comment '最小值',
        service_area       varchar(255)               not null comment '服务区',
        transmission_rate  varchar(255)               null default null comment '传输率',
        upload_rate        varchar(255)               null default null comment '上传率',
        system_code        varchar(255)               null default null comment '系统编码',
        service_code       varchar(255)               not null comment '服务区编码',
        discharge_amount   varchar(255) default '0.0' null default null comment '监测指标',
        average_value      decimal(19, 2)             null default null comment '平均值',
        `remark` varchar(255) null default null comment '备注',
        `create_time` datetime not null default current_timestamp comment '创建时间',
        `update_time` datetime not null default current_timestamp comment '更新时间',
        `creator` bigint(20) null default null comment '创建人',
        `modifier` bigint(20) null default null comment '更新人'
) comment '月报' collate = utf8mb4_general_ci;
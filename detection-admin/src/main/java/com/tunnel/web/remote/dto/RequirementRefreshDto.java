package com.tunnel.web.remote.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 设备刷新模型
 *
 * <AUTHOR>
 * @date 2025/8/5  18:02
 * @since 1.0.0
 */
@Data
@Builder
public class RequirementRefreshDto {


    /**
     * 需要新增的Topic
     */
    private List<String> addTopics;

    /**
     * 需要删除的Topic
     */
    private List<String> delTopics;

    /**
     * 来源
     */
    private String from ;
}

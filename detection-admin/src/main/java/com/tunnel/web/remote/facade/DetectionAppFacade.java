package com.tunnel.web.remote.facade;

import com.alibaba.fastjson2.JSON;
import com.tunnel.common.core.domain.Response;
import com.tunnel.web.remote.DetectionAppService;
import com.tunnel.web.remote.dto.RequirementRefreshDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/8/5  18:15
 * @since 1.0.0
 */
@Slf4j
@Service
public class DetectionAppFacade {

    // 注入Feign客户端
    @Autowired
    private DetectionAppService detectionAppService;

    @Async
    public void requirementRefresh(RequirementRefreshDto requirementRefresh) {
        log.info("请求参数: {}", JSON.toJSONString(requirementRefresh));
        Response<String> response = detectionAppService.refreshRequirement(requirementRefresh);
        log.info("Feign调用返回结果: {}", response);
    }
}

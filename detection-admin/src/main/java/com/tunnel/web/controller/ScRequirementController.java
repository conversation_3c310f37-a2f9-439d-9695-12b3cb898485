package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.ScRequirement;
import com.tunnel.service.ScRequirementService;
import com.tunnel.web.remote.dto.RequirementRefreshDto;
import com.tunnel.web.remote.facade.DetectionAppFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 设备信息Controller
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Slf4j
@RestController
@RequestMapping("/requirement/manage")
public class ScRequirementController extends BaseController
{
    @Autowired
    private ScRequirementService scRequirementService;
    @Resource
    private DetectionAppFacade detectionAppFacade;

    /**
     * 查询设备状态信息列表
     */
    //@PreAuthorize("@ss.hasPermi('requirement:manage:list')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody ScRequirement scRequirement)
    {
        startPage();
        List<ScRequirement> list = scRequirementService.selectScRequirementList(scRequirement);
        return getDataTable(list);
    }

    /**
     * 导出设备信息列表
     */
    //@PreAuthorize("@ss.hasPermi('requirement:manage:export')")
    @Log(title = "设备信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScRequirement scRequirement)
    {
        List<ScRequirement> list = scRequirementService.selectScRequirementList(scRequirement);
        ExcelUtil<ScRequirement> util = new ExcelUtil<ScRequirement>(ScRequirement.class);
        util.exportExcel(response, list, "设备信息数据");
    }

    /**
     * 获取设备信息详细信息
     */
    //@PreAuthorize("@ss.hasPermi('requirement:manage:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(scRequirementService.selectScRequirementById(id));
    }

    /**
     * 新增设备信息
     */
    //@PreAuthorize("@ss.hasPermi('requirement:manage:add')")
    @Log(title = "设备信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ScRequirement scRequirement)
    {
        int i = scRequirementService.insertScRequirement(scRequirement);
        detectionAppFacade.requirementRefresh(RequirementRefreshDto.builder().from(scRequirement.getDataFrom())
                .addTopics(Collections.singletonList(scRequirement.getTopic())).build());
        return toAjax(i);
    }

    /**
     * 修改设备信息
     */
    //@PreAuthorize("@ss.hasPermi('requirement:manage:edit')")
    @Log(title = "设备信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ScRequirement scRequirement)
    {
        int i = scRequirementService.updateScRequirement(scRequirement);
        detectionAppFacade.requirementRefresh(RequirementRefreshDto.builder().from(scRequirement.getDataFrom())
                .addTopics(Collections.singletonList(scRequirement.getTopic())).build());
        return toAjax(i);
    }

    /**
     * 删除设备信息
     */
    //@PreAuthorize("@ss.hasPermi('requirement:manage:remove')")
    @Log(title = "设备信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        //todo: 待完善功能
        return toAjax(scRequirementService.deleteScRequirementByIds(ids));
    }

    @PostMapping("/importTemplate")
    @ResponseBody
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<ScRequirement> util = new ExcelUtil<>(ScRequirement.class);
        util.exportExcel(response, Collections.emptyList(), "设备数据模板");
    }

    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file)
    {
        try (InputStream is = file.getInputStream()) {
            ExcelUtil<ScRequirement> util = new ExcelUtil<>(ScRequirement.class);
            List<ScRequirement> list =  util.importExcel(is);
            List<String> addTopics = new ArrayList<>();
            String dataFrom = null;
            for (ScRequirement item : list) {
                try {
                    scRequirementService.insertScRequirement(item);
                    addTopics.add(item.getTopic());
                    dataFrom = item.getDataFrom();
                } catch (Exception e) {
                    // 失败跳过
                    log.error("导入失败", e);
                }
            }
            if (CollectionUtils.isNotEmpty(addTopics)) {
                detectionAppFacade.requirementRefresh(RequirementRefreshDto.builder().from(dataFrom).addTopics(addTopics).build());
            }
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }

        return AjaxResult.success();
    }
}

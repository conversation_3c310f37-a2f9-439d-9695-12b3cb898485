package com.tunnel.web.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.MonitorStation;
import com.tunnel.service.MonitorStationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 监测站点Controller
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/detection/station")
public class MonitorStationController extends BaseController {
    @Autowired
    private MonitorStationService monitorStationService;

    /**
     * 查询监测站点列表
     */
    @PostMapping("/selectByPage")
    public TableDataInfo selectByPage(@RequestBody MonitorStation monitorStation) {
        Page page = PageHelper.startPage(monitorStation.getPageNum(), monitorStation.getPageSize());
        List<MonitorStation> list = monitorStationService.selectMonitorStationList(monitorStation);
        return getDataTable(list);
    }

    /**
     * 导出监测站点列表
     */
    @PreAuthorize("@ss.hasPermi('domain:station:export')")
    @Log(title = "监测站点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonitorStation monitorStation) {
        List<MonitorStation> list = monitorStationService.selectMonitorStationList(monitorStation);
        ExcelUtil<MonitorStation> util = new ExcelUtil<MonitorStation>(MonitorStation.class);
        util.exportExcel(response, list, "监测站点数据");
    }

    /**
     * 获取监测站点详细信息
     */
    @PreAuthorize("@ss.hasPermi('domain:station:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(monitorStationService.selectMonitorStationById(id));
    }

    /**
     * 新增监测站点
     */
    @PreAuthorize("@ss.hasPermi('domain:station:add')")
    @Log(title = "监测站点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MonitorStation monitorStation) {
        return toAjax(monitorStationService.insertMonitorStation(monitorStation));
    }

    /**
     * 修改监测站点
     */
    @PreAuthorize("@ss.hasPermi('domain:station:edit')")
    @Log(title = "监测站点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MonitorStation monitorStation) {
        return toAjax(monitorStationService.updateMonitorStation(monitorStation));
    }

    /**
     * 删除监测站点
     */
    @PreAuthorize("@ss.hasPermi('domain:station:remove')")
    @Log(title = "监测站点", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(monitorStationService.deleteMonitorStationByIds(ids));
    }
}

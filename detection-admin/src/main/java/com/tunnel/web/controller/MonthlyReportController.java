package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.MonthlyReport;
import com.tunnel.service.MonthlyReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 月报Controller
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/detection/report1")
public class MonthlyReportController extends BaseController
{
    @Autowired
    private MonthlyReportService monthlyReportService;

    /**
     * 查询月报列表
     */
    @PreAuthorize("@ss.hasPermi('domain:report:list')")
    @GetMapping("/list")
    public TableDataInfo list(MonthlyReport monthlyReport)
    {
        startPage();
        List<MonthlyReport> list = monthlyReportService.selectMonthlyReportList(monthlyReport);
        return getDataTable(list);
    }

    /**
     * 导出月报列表
     */
    @PreAuthorize("@ss.hasPermi('domain:report:export')")
    @Log(title = "月报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonthlyReport monthlyReport)
    {
        List<MonthlyReport> list = monthlyReportService.selectMonthlyReportList(monthlyReport);
        ExcelUtil<MonthlyReport> util = new ExcelUtil<MonthlyReport>(MonthlyReport.class);
        util.exportExcel(response, list, "月报数据");
    }

    /**
     * 获取月报详细信息
     */
    @PreAuthorize("@ss.hasPermi('domain:report:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(monthlyReportService.selectMonthlyReportById(id));
    }

    /**
     * 新增月报
     */
    @PreAuthorize("@ss.hasPermi('domain:report:add')")
    @Log(title = "月报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MonthlyReport monthlyReport)
    {
        return toAjax(monthlyReportService.insertMonthlyReport(monthlyReport));
    }

    /**
     * 修改月报
     */
    @PreAuthorize("@ss.hasPermi('domain:report:edit')")
    @Log(title = "月报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MonthlyReport monthlyReport)
    {
        return toAjax(monthlyReportService.updateMonthlyReport(monthlyReport));
    }

    /**
     * 删除月报
     */
    @PreAuthorize("@ss.hasPermi('domain:report:remove')")
    @Log(title = "月报", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(monthlyReportService.deleteMonthlyReportByIds(ids));
    }
}

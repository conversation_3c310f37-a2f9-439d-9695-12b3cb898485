package com.tunnel.web.controller;

import com.alibaba.fastjson2.JSONObject;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.enums.MonitorTypeEnum;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 监测类型Controller
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/monitor/type")
public class MonitorTypeController extends BaseController
{
    /**
     * 查询监测类型列表
     */
    @GetMapping("/list")
    public TableDataInfo list()
    {
        List<JSONObject> list = new ArrayList<>();
        for(MonitorTypeEnum monitorType : MonitorTypeEnum.values()) {
            list.add(new JSONObject().fluentPut("code", monitorType.getCode()).fluentPut("name", monitorType.getName()));
        }
        return getDataTable(list);
    }
}

package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.MonitorAlarmDevfault;
import com.tunnel.service.MonitorAlarmDevfaultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备故障报警Controller
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/detection/devfault")
public class MonitorAlarmDevfaultController extends BaseController
{
    @Autowired
    private MonitorAlarmDevfaultService monitorAlarmDevfaultService;

    /**
     * 查询设备故障报警列表
     */
    @PreAuthorize("@ss.hasPermi('domain:devfault:list')")
    @GetMapping("/list")
    public TableDataInfo list(MonitorAlarmDevfault monitorAlarmDevfault)
    {
        startPage();
        List<MonitorAlarmDevfault> list = monitorAlarmDevfaultService.selectMonitorAlarmDevfaultList(monitorAlarmDevfault);
        return getDataTable(list);
    }

    /**
     * 导出设备故障报警列表
     */
    @PreAuthorize("@ss.hasPermi('domain:devfault:export')")
    @Log(title = "设备故障报警", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonitorAlarmDevfault monitorAlarmDevfault)
    {
        List<MonitorAlarmDevfault> list = monitorAlarmDevfaultService.selectMonitorAlarmDevfaultList(monitorAlarmDevfault);
        ExcelUtil<MonitorAlarmDevfault> util = new ExcelUtil<MonitorAlarmDevfault>(MonitorAlarmDevfault.class);
        util.exportExcel(response, list, "设备故障报警数据");
    }

    /**
     * 获取设备故障报警详细信息
     */
    @PreAuthorize("@ss.hasPermi('domain:devfault:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(monitorAlarmDevfaultService.selectMonitorAlarmDevfaultById(id));
    }

    /**
     * 新增设备故障报警
     */
    @PreAuthorize("@ss.hasPermi('domain:devfault:add')")
    @Log(title = "设备故障报警", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MonitorAlarmDevfault monitorAlarmDevfault)
    {
        return toAjax(monitorAlarmDevfaultService.insertMonitorAlarmDevfault(monitorAlarmDevfault));
    }

    /**
     * 修改设备故障报警
     */
    @PreAuthorize("@ss.hasPermi('domain:devfault:edit')")
    @Log(title = "设备故障报警", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MonitorAlarmDevfault monitorAlarmDevfault)
    {
        return toAjax(monitorAlarmDevfaultService.updateMonitorAlarmDevfault(monitorAlarmDevfault));
    }

    /**
     * 删除设备故障报警
     */
    @PreAuthorize("@ss.hasPermi('domain:devfault:remove')")
    @Log(title = "设备故障报警", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(monitorAlarmDevfaultService.deleteMonitorAlarmDevfaultByIds(ids));
    }
}

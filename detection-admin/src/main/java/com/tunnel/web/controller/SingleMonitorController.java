package com.tunnel.web.controller;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.domain.MonitorDTO;
import com.tunnel.domain.MonitorStation;
import com.tunnel.domain.RequirementReport;
import com.tunnel.service.MonitorStationService;
import com.tunnel.service.RequirementReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 监测站点Controller
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/detection/single")
@Api(tags={"单点监测"})
public class SingleMonitorController extends BaseController {

    @Resource
    private MonitorStationService monitorStationService;
    @Resource
    private RequirementReportService requirementReportService;

    /**
     * 查询监测站点列表
     */
    @ApiOperation(value = "根据类型查询站点", produces = MediaType.APPLICATION_JSON_VALUE)
    @PostMapping("/selectMonitorStationByType")
    @Anonymous
    public AjaxResult selectMonitorStationByType(@RequestBody MonitorStation monitorStation) {
        List<MonitorStation> list = monitorStationService.selectMonitorStationByType(monitorStation);
        return AjaxResult.success(list);
    }



    /**
     * 查询监测站点列表
     */
    @ApiOperation(value = "根据类型查询有数据的一天", produces = MediaType.APPLICATION_JSON_VALUE)
    @PostMapping("/selectTimeRange")
    @Anonymous
    public AjaxResult selectTimeRange(@RequestBody MonitorDTO dto) {
        MonitorDTO list = monitorStationService.selectTimeRange(dto);
        return AjaxResult.success(list);
    }


    /**
     * 查询监测站点列表
     */
    @ApiOperation(value = "根据systemCode查询监控数据-分页", produces = MediaType.APPLICATION_JSON_VALUE)
    @PostMapping("/selectMonitorListByPage")
    @Anonymous
    public AjaxResult selectMonitorListByPage(@Validated @RequestBody MonitorDTO dto) {
        dto.setPage(true);
        if(Objects.isNull(dto.getPageNum()) || Objects.isNull(dto.getPageSize())){
            throw new ServiceException("分页参数不能为空");
        }
        PageInfo<Map<String, Object>> page = monitorStationService.selectMonitorListByPage(dto);
        return AjaxResult.success(page);
    }

    /**
     * 查询监测站点列表
     */
    @ApiOperation(value = "根据systemCode查询监控数据-图表格式", produces = MediaType.APPLICATION_JSON_VALUE)
    @PostMapping("/selectMonitorListForChart")
    @Anonymous
    public AjaxResult selectMonitorListForChart(@Validated @RequestBody MonitorDTO dto) {
        //时间跨度不能超过7天
        long daysBetween = DateUtil.betweenDay(dto.getStartTime(), dto.getEndTime(), true);
        if (daysBetween > 3) {
            throw new ServiceException("时间范围不能超过3天");
        }
        Map<String, Object> chartData = monitorStationService.selectMonitorListForChart(dto);
        return AjaxResult.success(chartData);
    }

    /**
     * 根据监测类型获取对应的字段信息
     */
    @ApiOperation(value = "根据监测类型获取字段信息", produces = MediaType.APPLICATION_JSON_VALUE)
    @PostMapping("/getFieldsByType")
    @Anonymous
    public AjaxResult getFieldsByType(@RequestBody MonitorDTO dto) {
        if (Objects.isNull(dto.getType())) {
            throw new ServiceException("监测类型不能为空");
        }
        if (Objects.isNull(dto.getSystemCode())) {
            throw new ServiceException("系统编码不能为空");
        }
        List<Map<String, Object>> fieldList = monitorStationService.getFieldsByType(dto.getType(), dto.getSystemCode());
        return AjaxResult.success(fieldList);
    }


}

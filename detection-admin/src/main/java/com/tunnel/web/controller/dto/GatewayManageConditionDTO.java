package com.tunnel.web.controller.dto;

import com.tunnel.common.utils.StringUtils;
import com.tunnel.enums.MonitorTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/14  15:25
 * @since 1.0.0
 */
@Data
public class GatewayManageConditionDTO {

    /**
     * 监测类型
     */
    private String monitorType;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 远程网关编码
     */
    private String processingGatewayCode;

    /**
     * 指标编码
     */
    private String pointAddr;

    /**
     * 开始时间
     */
    private Date startDateTime;

    /**
     * 结束时间
     */
    private Date endDateTime;

    public void check() {
        if (StringUtils.isEmpty(monitorType)) {
            monitorType = MonitorTypeEnum.SEWAGE.getCode();
        }
        handlerUserName();
    }

    private void handlerUserName() {
        if (this.userName != null && !this.userName.isEmpty()) {
            this.userName = this.userName.trim();
        } else {
            // todo获取当前登录用户的信息
        }
    }
}

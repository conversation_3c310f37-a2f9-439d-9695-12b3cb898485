package com.tunnel.web.controller.dto;

import com.tunnel.common.exception.user.ParamRequireException;
import com.tunnel.common.utils.StringUtils;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/14  15:25
 * @since 1.0.0
 */
@Data
public class SignalPointConditionDTO {

    /**
     * 监测类型
     */
    private String monitorType;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 服务站点编码
     */
    private String monitorStationCode;

    /**
     * 监测因子编码
     */
    private String factorCode;

    /**
     * 开始时间
     */
    private Date startDateTime;

    /**
     * 结束时间
     */
    private Date endDateTime;

    public void check() {
        if (StringUtils.isEmpty(monitorType)) {
            throw new ParamRequireException("监测类型必须有值");
        }
        handlerUserName();
    }

    private void handlerUserName() {
        if (this.userName != null && !this.userName.isEmpty()) {
            this.userName = this.userName.trim();
        } else {
            // todo获取当前登录用户的信息
        }
    }
}

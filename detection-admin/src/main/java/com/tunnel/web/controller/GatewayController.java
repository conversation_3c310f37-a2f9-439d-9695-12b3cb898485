package com.tunnel.web.controller;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.CheckDTO;
import com.tunnel.domain.Gateway;
import com.tunnel.service.GatewayService;
import com.tunnel.service.RequirementReportService;
import com.tunnel.web.remote.facade.VncServiceFacade;
import com.tunnel.web.service.GatewayAndDataManageService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 远程控制网关Controller
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/detection/gateway")
public class GatewayController extends BaseController {
    @Autowired
    private GatewayService gatewayService;
    @Resource
    private VncServiceFacade vncServiceFacade;

    @Autowired
    private RequirementReportService requirementReportService;

    @Resource
    private GatewayAndDataManageService gatewayAndDataManageService;

    /**
     * 获取指定网关的vnc地址
     */
    @PostMapping("/vncUrl")
    @Anonymous
    public AjaxResult gatewayVncUrl(@RequestBody CheckDTO dto) {
        return AjaxResult.success(gatewayAndDataManageService.getVncUrl(dto.getGatewayId()));
    }

    @PostMapping("/listVNCDevice")
    @Anonymous
    public AjaxResult listVNCDevice() {
        return AjaxResult.success(vncServiceFacade.listVNCDevice());
    }




    /**
     * 查询远程控制网关列表
     */
    @PostMapping("/listAll")
    @Anonymous
    public AjaxResult listAll(@RequestBody Gateway gateway) {
        List<Gateway> list = gatewayService.selectProcessingGatewayList(gateway);
        return AjaxResult.success(list);
    }

    /**
     * 根据监测类型获取对应的字段信息
     */
    @ApiOperation(value = "根据类型获取所有的字段", produces = MediaType.APPLICATION_JSON_VALUE, notes = "type:1.上报数据,2.状态数据,3.报警数据")
    @PostMapping("/getCheckFieldsByType")
    @Anonymous
    public AjaxResult getCheckFieldsByType(@RequestBody CheckDTO dto) {
        if (Objects.isNull(dto.getType())) {
            throw new ServiceException("监测类型不能为空");
        }
        List<Map<String, Object>> fieldList = requirementReportService.getCheckFieldsByType(dto.getType());
        return AjaxResult.success(fieldList);
    }

    /**
     * 查询监测站点列表
     */
    @ApiOperation(value = "根据gateWayId查询检测数据-分页", produces = MediaType.APPLICATION_JSON_VALUE)
    @PostMapping("/selectCheckListByPage")
    @Anonymous
    public AjaxResult selectCheckListByPage(@Validated @RequestBody CheckDTO dto) {
        dto.setPage(true);
        if (Objects.isNull(dto.getPageNum()) || Objects.isNull(dto.getPageSize())) {
            throw new ServiceException("分页参数不能为空");
        }
        PageInfo<Map<String, Object>> page = requirementReportService.selectCheckListByPage(dto);
        return AjaxResult.success(page);
    }


    /**
     * 查询监测站点列表
     */
    @ApiOperation(value = "根据类型查询有数据的一天", produces = MediaType.APPLICATION_JSON_VALUE)
    @PostMapping("/selectTimeRange")
    @Anonymous
    public AjaxResult selectTimeRange(@RequestBody CheckDTO dto) {
        CheckDTO list = requirementReportService.selectTimeRange(dto);
        return AjaxResult.success(list);
    }

    /**
     * 查询监测站点列表
     */
    @ApiOperation(value = "根据gateWayId查询检测数据-图表格式", produces = MediaType.APPLICATION_JSON_VALUE)
    @PostMapping("/selectCheckListForChart")
    @Anonymous
    public AjaxResult selectCheckListForChart(@Validated @RequestBody CheckDTO dto) {
        //时间跨度不能超过7天
        long daysBetween = DateUtil.betweenDay(dto.getStartTime(), dto.getEndTime(), true);
        if (daysBetween > 3) {
            throw new ServiceException("时间范围不能超过3天");
        }
        Map<String, Object> chartData = requirementReportService.selectCheckListForChart(dto);
        return AjaxResult.success(chartData);
    }


    /**
     * 导出远程控制网关列表
     */
    @PreAuthorize("@ss.hasPermi('domain:gateway:export')")
    @Log(title = "远程控制网关", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Gateway gateway) {
        List<Gateway> list = gatewayService.selectProcessingGatewayList(gateway);
        ExcelUtil<Gateway> util = new ExcelUtil<Gateway>(Gateway.class);
        util.exportExcel(response, list, "远程控制网关数据");
    }

    /**
     * 获取远程控制网关详细信息
     */
    @PreAuthorize("@ss.hasPermi('domain:gateway:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(gatewayService.selectProcessingGatewayById(id));
    }

    /**
     * 新增远程控制网关
     */
    @PreAuthorize("@ss.hasPermi('domain:gateway:add')")
    @Log(title = "远程控制网关", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Gateway gateway) {
        return toAjax(gatewayService.insertProcessingGateway(gateway));
    }

    /**
     * 修改远程控制网关
     */
    @PreAuthorize("@ss.hasPermi('domain:gateway:edit')")
    @Log(title = "远程控制网关", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Gateway gateway) {
        return toAjax(gatewayService.updateProcessingGateway(gateway));
    }

    /**
     * 删除远程控制网关
     */
    @PreAuthorize("@ss.hasPermi('domain:gateway:remove')")
    @Log(title = "远程控制网关", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(gatewayService.deleteProcessingGatewayByIds(ids));
    }
}

package com.tunnel.web.service.impl;

import com.tunnel.common.exception.user.ParamRequireException;
import com.tunnel.domain.IntegrationSystem;
import com.tunnel.domain.MonitorFactor;
import com.tunnel.domain.MonitorStation;
import com.tunnel.domain.MonitorStationRelation;
import com.tunnel.domain.MonitorStationUserRelation;
import com.tunnel.service.IntegrationSystemService;
import com.tunnel.service.MonitorFactorService;
import com.tunnel.service.MonitorStationRelationService;
import com.tunnel.service.MonitorStationService;
import com.tunnel.service.MonitorStationUserRelationService;
import com.tunnel.web.controller.dto.SignalPointConditionDTO;
import com.tunnel.web.service.SignalPointCheckService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/14  17:49
 * @since 1.0.0
 */
@Service
public class SignalPointCheckServiceImpl implements SignalPointCheckService {

    @Resource
    private MonitorStationService monitorStationService;
    @Resource
    private MonitorStationRelationService monitorStationRelationService;
    @Resource
    private MonitorStationUserRelationService monitorStationUserRelationService;
    @Resource
    private IntegrationSystemService integrationSystemService;
    @Resource
    private MonitorFactorService monitorFactorService;

    @Override
    public List<MonitorStation> selectMonitorStationList(SignalPointConditionDTO signalPointCondition) {
        signalPointCondition.check();
        List<MonitorStation> monitorStationList = monitorStationService.selectMonitorStationList(new MonitorStation());
        if (CollectionUtils.isEmpty(monitorStationList)) {
            return Collections.emptyList();
        }
        MonitorStationUserRelation monitorStationUserRelationQuery = MonitorStationUserRelation.builder()
                .userName(signalPointCondition.getUserName())
                .build();
        List<MonitorStationUserRelation> monitorStationUserRelationList = monitorStationUserRelationService.selectMonitorStationUserRelList(monitorStationUserRelationQuery);
        if (CollectionUtils.isEmpty(monitorStationUserRelationList)) {
            return Collections.emptyList();
        }
        MonitorStationRelation monitorStationRelationQuery = MonitorStationRelation.builder()
                .monitorType(signalPointCondition.getMonitorType())
                .build();
        List<MonitorStationRelation> monitorStationRelationList = monitorStationRelationService.selectMonitorStationRelationList(monitorStationRelationQuery);
        if (CollectionUtils.isEmpty(monitorStationRelationList)) {
            return Collections.emptyList();
        }
        Map<String, MonitorStationUserRelation> monitorStationUserRelationMap = monitorStationUserRelationList.stream().collect(Collectors.toMap(MonitorStationUserRelation::getServiceCode, Function.identity(), (v1, v2) -> v1));
        Map<String, MonitorStationRelation> monitorStationRelationMap = monitorStationRelationList.stream().collect(Collectors.toMap(MonitorStationRelation::getCode, Function.identity(), (v1, v2) -> v1));
        monitorStationList.removeIf(n -> !monitorStationUserRelationMap.containsKey(n.getCode()) && !monitorStationRelationMap.containsKey(n.getCode()));
        return monitorStationList;
    }

    @Override
    public List<MonitorFactor> selectMonitorFactorList(SignalPointConditionDTO signalPointCondition) {
        signalPointCondition.check();
        if (StringUtils.isEmpty(signalPointCondition.getMonitorStationCode())) {
            throw new ParamRequireException("站点编码不能为空");
        }
        IntegrationSystem integrationSystem = getSystem(signalPointCondition);
        if (integrationSystem == null) {
            return Collections.emptyList();
        }
//        MonitorFactor monitorFactorQuery = MonitorFactor.builder()
//                .monitoringSystemSystemCode(integrationSystem.getSystemCode())
//                .build();
//        List<MonitorFactor> monitorFactorList = monitorFactorService.selectMonitorFactorList(monitorFactorQuery);
        return null;
    }

    @Override
    public List<Map<String, Object>> selectMonitorFactorDataList(SignalPointConditionDTO signalPointCondition) {
        signalPointCondition.check();
        if (StringUtils.isEmpty(signalPointCondition.getMonitorStationCode())) {
            throw new ParamRequireException("站点编码不能为空");
        }
        if (StringUtils.isEmpty(signalPointCondition.getFactorCode())) {
            throw new ParamRequireException("监测因子不能为空");
        }
        if (signalPointCondition.getStartDateTime() == null && signalPointCondition.getEndDateTime() == null) {
            throw new RuntimeException("开始时间和结束时间至少一个有值");
        }

        IntegrationSystem integrationSystem = getSystem(signalPointCondition);
        if (integrationSystem == null) {
            return Collections.emptyList();
        }
//        MonitorFactor monitorFactorQuery = MonitorFactor.builder()
//                .monitoringSystemSystemCode(integrationSystem.getSystemCode())
//                .code(signalPointCondition.getFactorCode())
//                .build();
//        List<MonitorFactor> monitorFactorList = monitorFactorService.selectMonitorFactorList(monitorFactorQuery);
//        if (CollectionUtils.isEmpty(monitorFactorList)) {
//            return Collections.emptyList();
//        }
//        MonitorFactor monitorFactor = monitorFactorList.get(0);
        // todo: 查询数据，待实现
        return Collections.emptyList();
    }

    private IntegrationSystem getSystem(SignalPointConditionDTO signalPointCondition) {
        IntegrationSystem integrationSystemQuery = IntegrationSystem.builder()
                .monitorType(signalPointCondition.getMonitorType())
                .monitorStationCode(signalPointCondition.getMonitorStationCode())
                .build();
        List<IntegrationSystem> integrationSystemList = integrationSystemService.selectIntegrationSystemList(integrationSystemQuery);
        if (CollectionUtils.isEmpty(integrationSystemList)) {
            return null;
        }
        return integrationSystemList.get(0);
    }
}

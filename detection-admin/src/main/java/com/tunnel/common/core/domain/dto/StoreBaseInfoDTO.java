package com.tunnel.common.core.domain.dto;

import java.io.Serializable;

/**
 * 门店基础信息DTO
 * 
 * <AUTHOR>
 */
public class StoreBaseInfoDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 门店编码 */
    private String code;
    
    /** 门店名称 */
    private String name;
    
    /** 门店状态 */
    private String status;
    
    /** 省份编码 */
    private String provinceCode;
    
    /** 城市编码 */
    private String cityCode;
    
    // Getter and Setter methods
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getProvinceCode() {
        return provinceCode;
    }
    
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }
    
    public String getCityCode() {
        return cityCode;
    }
    
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }
}
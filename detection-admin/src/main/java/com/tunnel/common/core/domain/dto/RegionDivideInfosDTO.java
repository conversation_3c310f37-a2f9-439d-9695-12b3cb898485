package com.tunnel.common.core.domain.dto;

import java.io.Serializable;

/**
 * 地区划分信息DTO
 * 
 * <AUTHOR>
 */
public class RegionDivideInfosDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 地区编码 */
    private String areaCode;
    
    /** 地区名称 */
    private String areaName;
    
    /** 省份编码 */
    private String provinceCode;
    
    /** 省份名称 */
    private String provinceName;
    
    /** 地区类型 */
    private String type;
    
    // Getter and Setter methods
    public String getAreaCode() {
        return areaCode;
    }
    
    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }
    
    public String getAreaName() {
        return areaName;
    }
    
    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }
    
    public String getProvinceCode() {
        return provinceCode;
    }
    
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }
    
    public String getProvinceName() {
        return provinceName;
    }
    
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
}
package com.tunnel.common.core.domain.dto;

import java.io.Serializable;

/**
 * 门店主数据属性扩展DTO
 * 
 * <AUTHOR>
 */
public class StoreMdmAttributeExtDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 门店编码 */
    private String code;
    
    /** 门店名称 */
    private String name;
    
    /** 主数据属性 */
    private String mdmAttribute;
    
    /** 扩展属性 */
    private String extAttribute;
    
    // Getter and Setter methods
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getMdmAttribute() {
        return mdmAttribute;
    }
    
    public void setMdmAttribute(String mdmAttribute) {
        this.mdmAttribute = mdmAttribute;
    }
    
    public String getExtAttribute() {
        return extAttribute;
    }
    
    public void setExtAttribute(String extAttribute) {
        this.extAttribute = extAttribute;
    }
}
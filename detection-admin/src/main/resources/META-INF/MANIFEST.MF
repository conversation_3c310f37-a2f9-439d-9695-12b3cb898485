Manifest-Version: 1.0
Main-Class: com.tunnel.DetectionAdminApplication
Class-Path: spring-boot-starter-tomcat-2.5.14.jar reactor-core-3.4.18.ja
 r hibernate-validator-6.2.3.Final.jar slf4j-api-1.7.36.jar springfox-sc
 hema-3.0.0.jar quartz-2.3.2.jar spring-boot-starter-web-2.5.14.jar spri
 ng-oxm-5.3.20.jar javax.activation-api-1.2.0.jar pagehelper-5.3.1.jar t
 omcat-embed-core-9.0.63.jar spring-data-commons-2.5.11.jar jna-platform
 -5.12.1.jar curvesapi-1.06.jar swagger-annotations-1.6.2.jar jackson-co
 re-2.12.6.jar spring-context-5.3.20.jar logback-classic-1.2.11.jar spri
 ngfox-core-3.0.0.jar jakarta.annotation-api-1.3.5.jar spring-webmvc-5.3
 .20.jar springfox-oas-3.0.0.jar javax.servlet-api-4.0.1.jar poi-4.1.2.j
 ar jboss-logging-3.4.3.Final.jar spring-boot-2.5.14.jar poi-ooxml-schem
 as-4.1.2.jar springfox-spring-web-3.0.0.jar spring-expression-5.3.20.ja
 r spring-boot-starter-json-2.5.14.jar jackson-datatype-jdk8-2.12.6.jar 
 spring-plugin-core-2.0.0.RELEASE.jar springfox-spring-webmvc-3.0.0.jar 
 netty-common-4.1.77.Final.jar spring-web-5.3.20.jar netty-transport-4.1
 .77.Final.jar commons-collections4-4.4.jar spring-boot-starter-validati
 on-2.5.14.jar commons-collections-3.2.2.jar jul-to-slf4j-1.7.36.jar log
 back-core-1.2.11.jar spring-boot-devtools-2.5.14.jar spring-plugin-meta
 data-2.0.0.RELEASE.jar tomcat-embed-websocket-9.0.63.jar commons-math3-
 3.6.1.jar lombok-1.18.24.jar netty-resolver-4.1.77.Final.jar classmate-
 1.5.1.jar snakeyaml-1.28.jar springfox-bean-validators-3.0.0.jar pagehe
 lper-spring-boot-starter-1.4.3.jar classgraph-4.8.83.jar jackson-databi
 nd-2.12.6.1.jar mybatis-spring-boot-autoconfigure-2.2.2.jar spring-boot
 -starter-logging-2.5.14.jar swagger-annotations-2.1.2.jar xmlbeans-3.1.
 0.jar swagger-models-2.1.2.jar filters-2.0.235-1.jar springfox-swagger-
 common-3.0.0.jar spring-boot-starter-jdbc-2.5.14.jar mapstruct-1.3.1.Fi
 nal.jar jackson-annotations-2.12.6.jar poi-ooxml-4.1.2.jar netty-buffer
 -4.1.77.Final.jar spring-boot-starter-aop-2.5.14.jar spring-aop-5.3.20.
 jar log4j-api-2.17.2.jar jackson-datatype-jsr310-2.12.6.jar UserAgentUt
 ils-1.21.jar commons-compress-1.19.jar oshi-core-6.2.2.jar jaxb-api-2.3
 .1.jar springfox-boot-starter-3.0.0.jar commons-pool2-2.9.0.jar SparseB
 itSet-1.2.jar jjwt-0.9.1.jar spring-data-keyvalue-2.5.11.jar commons-co
 dec-1.15.jar druid-1.2.11.jar mybatis-3.5.9.jar springfox-swagger2-3.0.
 0.jar spring-tx-5.3.20.jar mybatis-spring-boot-starter-2.2.2.jar reacti
 ve-streams-1.0.3.jar tomcat-embed-el-9.0.63.jar jsqlparser-4.2.jar comm
 ons-lang3-3.12.0.jar byte-buddy-1.10.22.jar jna-5.12.1.jar springfox-sp
 i-3.0.0.jar mybatis-spring-2.0.7.jar commons-fileupload-1.4.jar spring-
 boot-starter-2.5.14.jar HikariCP-4.0.3.jar mchange-commons-java-0.2.15.
 jar springfox-spring-webflux-3.0.0.jar swagger-models-1.6.2.jar log4j-t
 o-slf4j-2.17.2.jar spring-beans-5.3.20.jar pagehelper-spring-boot-autoc
 onfigure-1.4.3.jar kaptcha-2.3.2.jar spring-core-5.3.20.jar fastjson2-2
 .0.14.jar jakarta.validation-api-2.0.2.jar spring-security-config-5.5.8
 .jar springfox-swagger-ui-3.0.0.jar mysql-connector-java-8.0.29.jar spr
 ing-boot-starter-security-2.5.14.jar spring-security-core-5.5.8.jar com
 mons-io-2.11.0.jar druid-spring-boot-starter-1.2.11.jar spring-context-
 support-5.3.20.jar netty-codec-4.1.77.Final.jar spring-boot-autoconfigu
 re-2.5.14.jar spring-boot-starter-data-redis-2.5.14.jar jackson-module-
 parameter-names-2.12.6.jar spring-security-crypto-5.5.8.jar spring-data
 -redis-2.5.11.jar spring-security-web-5.5.8.jar spring-jcl-5.3.20.jar s
 pringfox-data-rest-3.0.0.jar netty-handler-4.1.77.Final.jar aspectjweav
 er-1.9.7.jar spring-jdbc-5.3.20.jar lettuce-core-6.1.8.RELEASE.jar velo
 city-engine-core-2.3.jar


package com.tunnel.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import com.tunnel.domain.Road;

import java.util.ArrayList;
import java.util.List;

/**
 * Word文档公共处理工具类
 * 用于处理各个分册Word模板中的占位符替换
 * 
 * <AUTHOR>
 * @date 2024-12-09
 */
@Slf4j
public class WordDocumentUtils {

    /**
     * 替换Word文档中的占位符
     * @param document Word文档
     * @param placeholder 占位符
     * @param value 替换值
     */
    public  static void replaceTextInDocument(org.apache.poi.xwpf.usermodel.XWPFDocument document, String placeholder, String value) {
        // 替换段落中的文本
        for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : document.getParagraphs()) {
            replaceParagraphText(paragraph, placeholder, value);
        }

        // 替换表格中的文本
        for (org.apache.poi.xwpf.usermodel.XWPFTable table : document.getTables()) {
            for (org.apache.poi.xwpf.usermodel.XWPFTableRow row : table.getRows()) {
                for (org.apache.poi.xwpf.usermodel.XWPFTableCell cell : row.getTableCells()) {
                    for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceParagraphText(paragraph, placeholder, value);
                    }
                }
            }
        }

        // 替换页眉中的文本
        for (org.apache.poi.xwpf.usermodel.XWPFHeader header : document.getHeaderList()) {
            for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : header.getParagraphs()) {
                replaceParagraphText(paragraph, placeholder, value);
            }
        }

        // 替换页脚中的文本
        for (org.apache.poi.xwpf.usermodel.XWPFFooter footer : document.getFooterList()) {
            for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : footer.getParagraphs()) {
                replaceParagraphText(paragraph, placeholder, value);
            }
        }
    }


    /**
     * 替换Word文档中的占位符并设置字体
     * @param document Word文档
     * @param placeholder 占位符
     * @param value 替换值
     * @param fontName 字体名称
     */
    public  static void replaceTextInDocumentWithFont(org.apache.poi.xwpf.usermodel.XWPFDocument document, String placeholder, String value, String fontName) {
        // 替换段落中的文本
        for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : document.getParagraphs()) {
            replaceParagraphTextWithFont(paragraph, placeholder, value, fontName);
        }

        // 替换表格中的文本
        for (org.apache.poi.xwpf.usermodel.XWPFTable table : document.getTables()) {
            for (org.apache.poi.xwpf.usermodel.XWPFTableRow row : table.getRows()) {
                for (org.apache.poi.xwpf.usermodel.XWPFTableCell cell : row.getTableCells()) {
                    for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceParagraphTextWithFont(paragraph, placeholder, value, fontName);
                    }
                }
            }
        }

        // 替换页眉中的文本
        for (org.apache.poi.xwpf.usermodel.XWPFHeader header : document.getHeaderList()) {
            for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : header.getParagraphs()) {
                replaceParagraphTextWithFont(paragraph, placeholder, value, fontName);
            }
        }

        // 替换页脚中的文本
        for (org.apache.poi.xwpf.usermodel.XWPFFooter footer : document.getFooterList()) {
            for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : footer.getParagraphs()) {
                replaceParagraphTextWithFont(paragraph, placeholder, value, fontName);
            }
        }
    }

    /**
     * 替换段落中的文本并设置字体
     * @param paragraph 段落
     * @param placeholder 占位符
     * @param value 替换值
     * @param fontName 字体名称
     */
    public  static void replaceParagraphTextWithFont(org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph, String placeholder, String value, String fontName) {
        List<org.apache.poi.xwpf.usermodel.XWPFRun> runs = paragraph.getRuns();
        if (runs != null) {
            for (org.apache.poi.xwpf.usermodel.XWPFRun run : runs) {
                String text = run.getText(0);
                if (text != null && text.contains(placeholder)) {
                    text = text.replace(placeholder, value);
                    run.setText(text, 0);
                    // 设置字体
                    run.setFontFamily(fontName);
                    run.getCTR().getRPr().getRFonts().setEastAsia(fontName);
                }
            }
        }
    }

    /**
     * 替换段落中的文本
     */
    private static void replaceParagraphText(XWPFParagraph paragraph, String placeholder, String value) {
        try {
            String text = paragraph.getText();
            if (text != null && text.contains(placeholder)) {
                String newText = text.replace(placeholder, value != null ? value : "");

                // 保存第一个run的字体格式信息（如果存在的话）
                XWPFRun firstRun = null;
                String fontFamily = null;
                Integer fontSize = null;
                Boolean isBold = null;
                Boolean isItalic = null;
                String color = null;
                
                if (paragraph.getRuns().size() > 0) {
                    firstRun = paragraph.getRuns().get(0);
                    fontFamily = firstRun.getFontFamily();
                    fontSize = firstRun.getFontSize();
                    isBold = firstRun.isBold();
                    isItalic = firstRun.isItalic();
                    color = firstRun.getColor();
                }

                // 清除所有现有的runs
                int runs = paragraph.getRuns().size();
                for (int i = runs - 1; i >= 0; i--) {
                    paragraph.removeRun(i);
                }

                // 创建新的run并设置替换后的文本
                XWPFRun run = paragraph.createRun();
                run.setText(newText);
                
                // 应用保存的字体格式
                if (fontFamily != null) {
                    run.setFontFamily(fontFamily);
                }
                if (fontSize != null && fontSize > 0) {
                    run.setFontSize(fontSize);
                }
                if (isBold != null) {
                    run.setBold(isBold);
                }
                if (isItalic != null) {
                    run.setItalic(isItalic);
                }
                if (color != null && !color.isEmpty()) {
                    run.setColor(color);
                }
            }
        } catch (Exception e) {
            log.warn("替换段落文本失败: {}", e.getMessage());
        }
    }

    /**
     * 在段落中替换文本，完全保持原有格式
     * 这个方法逐个run进行处理，确保格式不被破坏
     */
    private static void replaceTextInParagraphPreservingFormat(XWPFParagraph paragraph, String placeholder, String replacement) {
        try {
            List<XWPFRun> runs = paragraph.getRuns();
            if (runs == null || runs.isEmpty()) {
                return;
            }

            // 构建完整的段落文本，同时记录每个字符对应的run索引
            StringBuilder fullText = new StringBuilder();
            List<Integer> charToRunMap = new ArrayList<>();
            
            for (int runIndex = 0; runIndex < runs.size(); runIndex++) {
                XWPFRun run = runs.get(runIndex);
                String runText = run.getText(0);
                if (runText != null) {
                    fullText.append(runText);
                    for (int i = 0; i < runText.length(); i++) {
                        charToRunMap.add(runIndex);
                    }
                }
            }

            String originalText = fullText.toString();
            if (!originalText.contains(placeholder)) {
                return;
            }

            // 找到占位符的位置
            int placeholderStart = originalText.indexOf(placeholder);
            int placeholderEnd = placeholderStart + placeholder.length();

            // 确定占位符所在的run范围
            if (placeholderStart >= 0 && placeholderStart < charToRunMap.size()) {
                int targetRunIndex = charToRunMap.get(placeholderStart);
                XWPFRun targetRun = runs.get(targetRunIndex);

                // 保存目标run的所有格式信息
                String fontFamily = targetRun.getFontFamily();
                Integer fontSize = targetRun.getFontSize();
                Boolean isBold = targetRun.isBold();
                Boolean isItalic = targetRun.isItalic();
                String color = targetRun.getColor();
                Boolean isUnderlined = targetRun.getUnderline() != null;

                // 替换文本
                String newText = originalText.replace(placeholder, replacement);

                // 清除所有run的文本
                for (XWPFRun run : runs) {
                    run.setText("", 0);
                }

                // 将新文本设置到目标run中，保持其格式
                targetRun.setText(newText, 0);

                // 确保格式完全保持
                if (fontFamily != null) {
                    targetRun.setFontFamily(fontFamily);
                }
                if (fontSize != null && fontSize > 0) {
                    targetRun.setFontSize(fontSize);
                }
                if (isBold != null) {
                    targetRun.setBold(isBold);
                }
                if (isItalic != null) {
                    targetRun.setItalic(isItalic);
                }
                if (color != null && !color.isEmpty()) {
                    targetRun.setColor(color);
                }
                if (isUnderlined != null && isUnderlined) {
                    targetRun.setUnderline(targetRun.getUnderline());
                }

                log.debug("页眉标题替换成功：{} -> {}，保持原格式（字体: {}, 大小: {}）", 
                         placeholder, replacement, fontFamily, fontSize);
            }
        } catch (Exception e) {
            log.warn("保持格式的文本替换失败: {}", e.getMessage());
            // 如果精确替换失败，回退到简单替换
            try {
                String text = paragraph.getText();
                if (text != null && text.contains(placeholder)) {
                    // 简单的整体替换作为备用方案
                    for (XWPFRun run : paragraph.getRuns()) {
                        String runText = run.getText(0);
                        if (runText != null && runText.contains(placeholder)) {
                            run.setText(runText.replace(placeholder, replacement), 0);
                            break;
                        }
                    }
                }
            } catch (Exception fallbackException) {
                log.warn("备用文本替换也失败: {}", fallbackException.getMessage());
            }
        }
    }
}
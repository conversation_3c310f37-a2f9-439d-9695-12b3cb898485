package com.tunnel.domain;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;

@Data
public class RequirementReport extends BaseEntity {
    private String id;
    private String gatewayId;
    private String key; // b_key
    private String topic;

    // I16系列字段
    private String i160; private String i161; private String i162; private String i163; private String i164; private String i165; private String i166; private String i167;
    // I17系列字段
    private String i170; private String i171; private String i172; private String i173; private String i174;
    
    private String i200; private String i201; private String i202; private String i203; private String i204; private String i205; private String i206;
    private String i210; private String i211; private String i212; private String i213; private String i214;
    private String vd200; private String vd230; private String vd260; private String vd2068;
    private String vd404; private String vd408;
    private String vd704; private String vd712; private String vd716;
    private String vd720; private String vd728; private String vd736; private String vd740;
    private String vd744; private String vd752; private String vd760; private String vd764;
    private String vd904; private String vd912; private String vd916; private String vd928; private String vd936; private String vd940; private String vd952; private String vd960; private String vd964;
    private String vd4020; private String vd4028;

    private String remark;
    private Long creator;
    private Long modifier;

}



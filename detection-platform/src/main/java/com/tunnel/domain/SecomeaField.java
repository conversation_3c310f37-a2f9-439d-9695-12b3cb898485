package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

/**
 * 控制点位对象 secomea_field
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Builder
@ToString
public class SecomeaField extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 是否选中展示 */
    @Excel(name = "是否选中展示")
    private Integer checked;

    /** 控制点位编码 */
    @Excel(name = "控制点位编码")
    private String pointAddr;

    /** 控制点位名称 */
    @Excel(name = "控制点位名称")
    private String name;

    /** 关联的远程网关code */
    @Excel(name = "关联的远程网关code")
    private String processingGatewayCode;

    /** 监测类型编码 */
    @Excel(name = "监测类型编码")
    private String monitorType;

    /** 监测类型名称 */
    @Excel(name = "监测类型名称")
    private String monitorTypeName;
}

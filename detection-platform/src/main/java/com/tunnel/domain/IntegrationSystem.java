package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

/**
 * 系统信息对象 integration_system
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Builder
@ToString
public class IntegrationSystem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 类型,是否接入系统 */
    @Excel(name = "类型,是否接入系统")
    private String type;

    /** 系统编码 */
    @Excel(name = "系统编码")
    private String systemCode;

    /** 监测类型编码 */
    @Excel(name = "监测类型编码")
    private String monitorType;

    /** 监测类型编码 */
    @Excel(name = "监测类型名称")
    private String monitorTypeName;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 关联表monitor_station */
    @Excel(name = "关联表monitor_station")
    private String monitorStationCode;
}

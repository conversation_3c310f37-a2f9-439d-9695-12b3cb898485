package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.ToString;

/**
 * 设备状态信息对象 sc_requirement
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@ToString
public class ScRequirement extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private String id;

    /** 设备编码 */
    @Excel(name = "设备编码")
    private String code;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String name;

    /** 设备类型 */
    @Excel(name = "设备类型")
    private String type;

    /** 设备类型 */
    @Excel(name = "设备类型")
    private String typeName;

    /** mac地址 */
    @Excel(name = "mac地址")
    private String macAddress;

    /** ip */
    @Excel(name = "ip")
    private String ip;

    /** 路线id */
    @Excel(name = "路线id")
    private Long roadId;

    /** 数据来源，mqtt， */
    @Excel(name = "数据来源，mqtt，")
    private String dataFrom;

    /** 消息方式的topic */
    @Excel(name = "消息方式的topic")
    private String topic;

    @Excel(name = "路线")
    private String roadName;
}

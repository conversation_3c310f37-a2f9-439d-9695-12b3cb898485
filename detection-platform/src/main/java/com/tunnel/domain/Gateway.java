package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

/**
 * 远程控制网关对象 processing_gateway
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class Gateway extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 远程网关编码
     */
    @Excel(name = "远程网关编码")
    private String code;

    /**
     * mac地址
     */
    @Excel(name = "mac地址")
    private String mac;

    /**
     * 网关名称
     */
    @Excel(name = "网关名称")
    private String name;
}

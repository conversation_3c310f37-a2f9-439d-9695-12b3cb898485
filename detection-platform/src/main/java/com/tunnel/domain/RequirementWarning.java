package com.tunnel.domain;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;

@Data
public class RequirementWarning extends BaseEntity {
    private String id;
    private String gatewayId;
    private String key; // b_key
    private String topic;

    // v91_*, v92_*, v93_*, v94_*, v95_*, v96_*, dcm_alarm
    private String v910; private String v911; private String v912; private String v913; private String v914; private String v915; private String v916; private String v917;
    private String v920; private String v921; private String v922; private String v923; private String v924; private String v925; private String v926; private String v927;
    private String v930; private String v931; private String v932; private String v933; private String v934; private String v935; private String v936; private String v937;
    private String v940; private String v942; private String v950; private String v951; private String v952; private String v953; private String v954; private String v955; private String v956; private String v957;
    private String v960;
    private String dcmAlarm;


}



package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 监测站点对象 monitor_station
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ToString
public class MonitorStation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private String type;

    /**
     * 站点编码
     */
    @Excel(name = "站点编码")
    private String code;

    /**
     * 城市
     */
    @Excel(name = "城市")
    private String city;

    /**
     * 地区
     */
    @Excel(name = "地区")
    private String district;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private BigDecimal lat;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private BigDecimal lon;

    /**
     * 站点名称
     */
    @Excel(name = "站点名称")
    private String name;

    /**
     * 省份
     */
    @Excel(name = "省份")
    private String province;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Integer state;

    /**
     * 污染类型
     */
    @Excel(name = "污染类型")
    private String pollutionType;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Integer faulty;

    /**
     * 站点顺序
     */
    @Excel(name = "站点顺序")
    private Integer stationindex;

    @ApiModelProperty(value = "系统编码")
    private String systemCode;

    @Excel(name = "页码")
    private Integer pageNum=1;

    @Excel(name = "页面大小")
    private Integer pageSize=10;
}

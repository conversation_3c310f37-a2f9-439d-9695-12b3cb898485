package com.tunnel.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 审核请求DTO
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
public class AuditRequest {
    
    /** 记录ID列表 */
    @NotEmpty(message = "记录ID不能为空")
    private List<Long> ids;
    
    /** 审核状态：1-审核通过，2-不通过 */
    @NotNull(message = "审核状态不能为空")
    private Integer checkStatus;
    
    /** 审核备注 */
    private String remark;

    /**
     * 上报类型
     * 0: 桥梁
     * 1: 隧道
     * 2: 涵洞
     * 3: 边坡
     * 4: 路面
     */
    private Integer type;
}
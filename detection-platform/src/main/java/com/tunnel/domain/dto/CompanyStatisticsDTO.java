package com.tunnel.domain.dto;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 公司维度统计数据DTO
 * <AUTHOR>
 */
@Data
public class CompanyStatisticsDTO extends BaseEntity {
    /** 序号 */
    @Excel(name = "序号", cellType = Excel.ColumnType.NUMERIC)
    private Integer rowNum;
    
    /** 公司名称 */
    @Excel(name = "公司名称", cellType = Excel.ColumnType.STRING)
    private String companyName;
    
    /** 上报总数 */
    @Excel(name = "上报总数", cellType = Excel.ColumnType.NUMERIC)
    private Integer totalReports;
    
    /** 桥梁检测数量 */
    @Excel(name = "桥梁", cellType = Excel.ColumnType.NUMERIC)
    private Integer bridgeCount;
    
    /** 隧道检测数量 */
    @Excel(name = "隧道", cellType = Excel.ColumnType.NUMERIC)
    private Integer tunnelCount;
    
    /** 涵洞检测数量 */
    @Excel(name = "涵洞", cellType = Excel.ColumnType.NUMERIC)
    private Integer holeCount;
    
    /** 边坡检测数量 */
    @Excel(name = "边坡", cellType = Excel.ColumnType.NUMERIC)
    private Integer slopeCount;
    
    /** 路面检测数量 */
    @Excel(name = "路面", cellType = Excel.ColumnType.NUMERIC)
    private Integer roadCount;
    
    /** 已维修数量 */
    @Excel(name = "已维修", cellType = Excel.ColumnType.NUMERIC)
    private Integer repairedCount;
    
    /** 维修完成率 */
    @Excel(name = "维修完成率", cellType = Excel.ColumnType.STRING)
    private String repairRate;
    
    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    private String remark;
} 
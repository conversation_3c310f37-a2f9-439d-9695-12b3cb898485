package com.tunnel.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 隧道检查及整改信息DTO
 * <AUTHOR>
 */
@Data
public class TunnelRepairDTO {
    /** 隧道检查ID */
    private Long id;
    
    /** 整改记录ID */
    private Long repairId;
    
    /** 路线ID */
    private Long roadId;
    
    /** 公司名称 */
    private String companyName;
    
    /** 路线编码 */
    private String roadCode;
    
    /** 路线名称 */
    private String roadName;
    
    /** 隧道名称 */
    private String tunnelName;
    
    /** 方向 */
    private String direction;
    
    /** 风险因素 */
    private String riskFactor;
    
    /** 缺陷描述 */
    private String defectDesc;
    
    /** 风险等级 */
    private String riskLevel;
    
    /** 是否紧急 (0:否 1:是) */
    private Integer isUrgent;
    
    /** 发现时间 */
    private Date findTime;
    
    /** 上报照片（多张照片以逗号分隔） */
    private String reportPhotos;
    
    /** 整改截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date repairEndTime;

    /** 检测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    
    /** 整改状态（0:待整改 1:已整改） */
    private Integer repairStatus;
    
    /** 整改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date repairTime;
    
    /** 整改记录 */
    private String repairRecord;
    
    /** 整改照片（多张照片以逗号分隔） */
    private String repairPhotos;
}
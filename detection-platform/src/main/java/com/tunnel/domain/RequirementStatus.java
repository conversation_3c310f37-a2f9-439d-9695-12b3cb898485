package com.tunnel.domain;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;

@Data
public class RequirementStatus extends BaseEntity {
    private String id;
    private String gatewayId;
    private String key; // b_key
    private String topic;

    // vb100 - vb158 (as provided)
    private String vb100; private String vb101; private String vb102; private String vb103; private String vb104;
    private String vb105; private String vb106; private String vb107; private String vb108; private String vb109;
    private String vb110; private String vb111; private String vb112; private String vb113; private String vb114;
    private String vb115; private String vb116; private String vb117; private String vb118; private String vb156;
    private String vb119; private String vb120; private String vb121; private String vb122; private String vb157;
    private String vb123; private String vb124; private String vb125; private String vb126; private String vb127;
    private String vb128; private String vb129; private String vb155; private String vb133; private String vb134;
    private String vb135; private String vb136; private String vb137; private String vb138; private String vb139;
    private String vb140; private String vb130; private String vb141; private String vb131; private String vb132;
    private String vb142; private String vb143; private String vb144; private String vb145; private String vb146;
    private String vb147; private String vb148; private String vb149; private String vb150; private String vb151;
    private String vb158;
}



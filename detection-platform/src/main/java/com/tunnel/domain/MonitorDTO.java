package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NonNull;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 监测站点对象 monitor_station
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ToString
public class MonitorDTO extends Pagination {

    @ApiModelProperty(value = "检测类型:类型:1.污水,2.大气,3.噪声,4.固废")
    @NotNull(message = "检测类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "站点监控编号")
    @NotBlank(message = "站点监控编号不能为空")
    private String systemCode;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    private boolean page;

}

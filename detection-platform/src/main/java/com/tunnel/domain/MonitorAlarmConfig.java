package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 监测预警配置对象 monitor_alarm_config
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ToString
public class MonitorAlarmConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private Long id;

    /** 是否启用 */
    @Excel(name = "是否启用")
    private Integer alarmEnabled;

    /** 预警上限 */
    @Excel(name = "预警上限")
    private BigDecimal alarmLimit;

    /** 监测因子编码 */
    @Excel(name = "监测因子编码")
    private String factorId;

    /** 服务区编码 */
    @Excel(name = "服务区编码")
    private String serviceId;

    /** 系统编码 */
    @Excel(name = "系统编码")
    private String systemCode;

    /** 系统名称 */
    @Excel(name = "系统名称")
    private String systemName;

    /** 监测因子名称 */
    @Excel(name = "监测因子名称")
    private String factorName;

    /** 最大值 */
    @Excel(name = "最大值")
    private BigDecimal max;

    /** 最小值 */
    @Excel(name = "最小值")
    private BigDecimal min;

    /** 服务区名称 */
    @Excel(name = "服务区名称")
    private String serviceArea;

    /** 预警名称 */
    @Excel(name = "预警名称")
    private String name;

    /** 电子邮箱 */
    @Excel(name = "电子邮箱")
    private String emailAddress;

    /** 是否启用电子邮箱 */
    @Excel(name = "是否启用电子邮箱")
    private Integer emailEnabled;

    /** 上次邮件通知时间 */
    @Excel(name = "上次邮件通知时间")
    private String lastTime;

    /** 是否短信提醒 */
    @Excel(name = "是否短信提醒")
    private Integer messageEnabled;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phoneNum;

    /** 邮件通知时间间隔 */
    @Excel(name = "邮件通知时间间隔")
    private Long timeInterval;

    /** 上次短信通知时间 */
    @Excel(name = "上次短信通知时间")
    private String lastMessageTime;

    /** 短信通知时间间隔 */
    @Excel(name = "短信通知时间间隔")
    private Long messageTimeInterval;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal floorAlarmLimit;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Integer pairAlarm;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String pollutionType;
}

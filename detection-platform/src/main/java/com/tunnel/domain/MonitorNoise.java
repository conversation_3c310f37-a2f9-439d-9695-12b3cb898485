package com.tunnel.domain;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 噪声监测数据对象 sc_monitor_noise
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class MonitorNoise extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 监测系统编码 */
    private String monitorCode;

    /** 业务标识key */
    private String bKey;

    /** 路由 */
    private String topic;

    /** 累计百分声级L10 */
    private BigDecimal l10;

    /** 昼夜等效声级 */
    private BigDecimal ldn;

    /** 夜间等效声级 */
    private BigDecimal ln;

    /** 累计百分声级L50 */
    private BigDecimal l50;

    /** 累计百分声级L5 */
    private BigDecimal l5;

    /** 昼间等效声级 */
    private BigDecimal ld;

    /** PRF */
    private BigDecimal prf;

    /** 累计百分声级L95 */
    private BigDecimal l95;

    /** Rate */
    private BigDecimal rate;

    /** 累计百分声级L90 */
    private BigDecimal l90;

    /** SD */
    private BigDecimal sd;

    /** Leq */
    private BigDecimal leq;

    /** 备注 */
    private String remark;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;
}

package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

/**
 * 监测站点和用户关联对象 monitor_station_user_rel
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Builder
@ToString
public class MonitorStationUserRelation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 类型 */
    @Excel(name = "类型")
    private String serviceCode;

    /** 用户名 */
    @Excel(name = "用户名")
    private String userName;
}

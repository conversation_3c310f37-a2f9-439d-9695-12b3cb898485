package com.tunnel.domain;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 水质监测数据对象 sc_monitor_data
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class MonitorSewage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 监测系统编码 */
    private String monitorCode;

    /** 业务标识key */
    private String bKey;

    /** 路由 */
    private String topic;

    /** pH(酸碱度) */
    private String ph;

    /** WT(水温) */
    private String wt;

    /** COD(化学需氧量) */
    private String cod;

    /** TN(总氮) */
    private String tn;

    /** NH3-N(氨氮) */
    private String nh3n;

    /** TP(总磷) */
    private String tp;

    /** OIL(石油类) */
    private String oil;

    /** SS(浊度) */
    private String ss;

    /** SWTP(出水总磷) */
    private String swtp;

    /** SWNH4-N(出水氨氮) */
    private String swnh4n;

    /** SWCOD(出水COD) */
    private String swcod;

    /** SWOIL(出水水中油) */
    private String swoil;

    /** TURB(出水浊度) */
    private String turb;

    /** FLOW(流量) */
    private String flow;

    /** WFLOAT(累计水流量) */
    private String wfloat;

    /** WRTFLO(瞬时流量) */
    private String wrtflo;

    /** HIGHT(液位高度) */
    private String hight;

    /** 备注 */
    private String remark;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;
}

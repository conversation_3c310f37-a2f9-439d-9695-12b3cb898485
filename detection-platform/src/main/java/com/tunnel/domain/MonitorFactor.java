package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 监测因子对象 monitor_factor
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class MonitorFactor extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 监测因子编码
     */
    @Excel(name = "监测因子编码")
    private String code;

    /**
     * 最大值
     */
    @Excel(name = "最大值")
    private BigDecimal maxValue;

    /**
     * 最小值
     */
    @Excel(name = "最小值")
    private BigDecimal minValue;

    /**
     * 监测因子名称
     */
    @Excel(name = "监测因子名称")
    private String name;

    /**
     * 设备ID
     */
    @Excel(name = "设备ID")
    private String sensorId;

    /**
     * 单位
     */
    @Excel(name = "单位")
    private String unit;

    /**
     * 系统编码
     */
    @Excel(name = "系统编码")
    private String monitoringSystemSystemCode;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Integer exceeded;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Integer faulty;

    /**
     * 因子排序
     */
    @Excel(name = "因子排序")
    private Integer factorIndex;


    @Excel(name = "页码")
    private Integer pageNum = 1;

    @Excel(name = "页面大小")
    private Integer pageSize = 10;
}

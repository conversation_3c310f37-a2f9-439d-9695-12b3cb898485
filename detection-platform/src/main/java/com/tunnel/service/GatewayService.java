package com.tunnel.service;

import com.tunnel.domain.Gateway;

import java.util.List;

/**
 * 远程控制网关Service接口
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface GatewayService {
    /**
     * 查询远程控制网关
     *
     * @param id 远程控制网关主键
     * @return 远程控制网关
     */
    public Gateway selectProcessingGatewayById(Long id);

    /**
     * 查询远程控制网关列表
     *
     * @param gateway 远程控制网关
     * @return 远程控制网关集合
     */
    public List<Gateway> selectProcessingGatewayList(Gateway gateway);

    /**
     * 新增远程控制网关
     *
     * @param gateway 远程控制网关
     * @return 结果
     */
    public int insertProcessingGateway(Gateway gateway);

    /**
     * 修改远程控制网关
     *
     * @param gateway 远程控制网关
     * @return 结果
     */
    public int updateProcessingGateway(Gateway gateway);

    /**
     * 批量删除远程控制网关
     *
     * @param ids 需要删除的远程控制网关主键集合
     * @return 结果
     */
    public int deleteProcessingGatewayByIds(Long[] ids);

    /**
     * 删除远程控制网关信息
     *
     * @param id 远程控制网关主键
     * @return 结果
     */
    public int deleteProcessingGatewayById(Long id);

    /**
     * 查询所有控制网关列表
     *
     * @return 远程控制网关集合
     */
    public List<Gateway> selectProcessingGatewayAll();

}

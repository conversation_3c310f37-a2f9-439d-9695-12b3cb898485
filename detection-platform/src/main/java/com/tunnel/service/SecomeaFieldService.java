package com.tunnel.service;

import com.tunnel.domain.SecomeaField;

import java.util.List;

/**
 * 控制点位Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface SecomeaFieldService
{
    /**
     * 查询控制点位
     * 
     * @param id 控制点位主键
     * @return 控制点位
     */
    public SecomeaField selectSecomeaFieldById(Long id);

    /**
     * 查询控制点位列表
     * 
     * @param secomeaField 控制点位
     * @return 控制点位集合
     */
    public List<SecomeaField> selectSecomeaFieldList(SecomeaField secomeaField);

    /**
     * 新增控制点位
     * 
     * @param secomeaField 控制点位
     * @return 结果
     */
    public int insertSecomeaField(SecomeaField secomeaField);

    /**
     * 修改控制点位
     * 
     * @param secomeaField 控制点位
     * @return 结果
     */
    public int updateSecomeaField(SecomeaField secomeaField);

    /**
     * 批量删除控制点位
     * 
     * @param ids 需要删除的控制点位主键集合
     * @return 结果
     */
    public int deleteSecomeaFieldByIds(Long[] ids);

    /**
     * 删除控制点位信息
     * 
     * @param id 控制点位主键
     * @return 结果
     */
    public int deleteSecomeaFieldById(Long id);
}

package com.tunnel.service.impl;

import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.Road;
import com.tunnel.domain.ScRequirement;
import com.tunnel.mapper.ScRequirementMapper;
import com.tunnel.service.ScRequirementService;
import com.tunnel.service.RoadService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 设备状态信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class ScRequirementServiceImpl implements ScRequirementService
{
    @Autowired
    private ScRequirementMapper scRequirementMapper;
    @Resource
    private RoadService roadService;

    /**
     * 查询设备信息
     * 
     * @param id 设备信息主键
     * @return 设备信息
     */
    @Override
    public ScRequirement selectScRequirementById(Long id)
    {
        ScRequirement requirement = scRequirementMapper.selectScRequirementById(id);
        supplementRoad(Collections.singletonList(requirement));
        return requirement;
    }

    /**
     * 查询设备信息列表
     * 
     * @param scRequirement 设备信息
     * @return 设备信息
     */
    @Override
    public List<ScRequirement> selectScRequirementList(ScRequirement scRequirement)
    {
        List<ScRequirement> list = scRequirementMapper.selectScRequirementList(scRequirement);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        supplementRoad(list);
        return list;
    }

    /**
     * 新增设备信息
     * 
     * @param scRequirement 设备信息
     * @return 结果
     */
    @Override
    public int insertScRequirement(ScRequirement scRequirement)
    {
        scRequirement.setCreateTime(DateUtils.getNowDate());
        return scRequirementMapper.insertScRequirement(scRequirement);
    }

    /**
     * 修改设备信息
     * 
     * @param scRequirement 设备信息
     * @return 结果
     */
    @Override
    public int updateScRequirement(ScRequirement scRequirement)
    {
        scRequirement.setUpdateTime(DateUtils.getNowDate());
        return scRequirementMapper.updateScRequirement(scRequirement);
    }

    /**
     * 批量删除设备信息
     * 
     * @param ids 需要删除的设备信息主键
     * @return 结果
     */
    @Override
    public int deleteScRequirementByIds(String[] ids)
    {
        return scRequirementMapper.deleteScRequirementByIds(ids);
    }

    /**
     * 删除设备信息信息
     * 
     * @param id 设备信息主键
     * @return 结果
     */
    @Override
    public int deleteScRequirementById(String id)
    {
        return scRequirementMapper.deleteScRequirementById(id);
    }

    private void supplementRoad(List<ScRequirement> requirementList) {
        if (CollectionUtils.isEmpty(requirementList)) {
            return;
        }

        List<Long> roadIds = requirementList.stream()
                .map(ScRequirement::getRoadId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        List<Road> roadList = roadService.selectRoadListByIds(roadIds);
        Map<Long, Road> roadMap = roadList.stream().collect(Collectors.toMap(Road::getId, Function.identity(), (v1, v2) -> v1));
        requirementList.forEach(item -> {
            Road road = roadMap.get(item.getRoadId());
            if (road != null) {
                item.setRoadName(road.getCompanyName() + "-" + road.getRoadCode() + road.getRoadName());
            }
        });
    }
}

package com.tunnel.service.impl;

import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.MonitorStationRelationInfo;
import com.tunnel.mapper.MonitorStationRelationInfoMapper;
import com.tunnel.service.MonitorStationRelationInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 监测站点详细信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class MonitorStationRelationInfoServiceImpl implements MonitorStationRelationInfoService
{
    @Autowired
    private MonitorStationRelationInfoMapper monitorStationRelationInfoMapper;

    /**
     * 查询监测站点详细信息
     * 
     * @param id 监测站点详细信息主键
     * @return 监测站点详细信息
     */
    @Override
    public MonitorStationRelationInfo selectMonitorStationRelationInfoById(Long id)
    {
        return monitorStationRelationInfoMapper.selectMonitorStationRelationInfoById(id);
    }

    /**
     * 查询监测站点详细信息列表
     * 
     * @param monitorStationRelationInfo 监测站点详细信息
     * @return 监测站点详细信息
     */
    @Override
    public List<MonitorStationRelationInfo> selectMonitorStationRelationInfoList(MonitorStationRelationInfo monitorStationRelationInfo)
    {
        return monitorStationRelationInfoMapper.selectMonitorStationRelationInfoList(monitorStationRelationInfo);
    }

    /**
     * 新增监测站点详细信息
     * 
     * @param monitorStationRelationInfo 监测站点详细信息
     * @return 结果
     */
    @Override
    public int insertMonitorStationRelationInfo(MonitorStationRelationInfo monitorStationRelationInfo)
    {
        monitorStationRelationInfo.setCreateTime(DateUtils.getNowDate());
        return monitorStationRelationInfoMapper.insertMonitorStationRelationInfo(monitorStationRelationInfo);
    }

    /**
     * 修改监测站点详细信息
     * 
     * @param monitorStationRelationInfo 监测站点详细信息
     * @return 结果
     */
    @Override
    public int updateMonitorStationRelationInfo(MonitorStationRelationInfo monitorStationRelationInfo)
    {
        monitorStationRelationInfo.setUpdateTime(DateUtils.getNowDate());
        return monitorStationRelationInfoMapper.updateMonitorStationRelationInfo(monitorStationRelationInfo);
    }

    /**
     * 批量删除监测站点详细信息
     * 
     * @param ids 需要删除的监测站点详细信息主键
     * @return 结果
     */
    @Override
    public int deleteMonitorStationRelationInfoByIds(Long[] ids)
    {
        return monitorStationRelationInfoMapper.deleteMonitorStationRelationInfoByIds(ids);
    }

    /**
     * 删除监测站点详细信息信息
     * 
     * @param id 监测站点详细信息主键
     * @return 结果
     */
    @Override
    public int deleteMonitorStationRelationInfoById(Long id)
    {
        return monitorStationRelationInfoMapper.deleteMonitorStationRelationInfoById(id);
    }
}

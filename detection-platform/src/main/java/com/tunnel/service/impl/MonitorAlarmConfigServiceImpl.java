package com.tunnel.service.impl;

import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.MonitorAlarmConfig;
import com.tunnel.mapper.MonitorAlarmConfigMapper;
import com.tunnel.service.MonitorAlarmConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 监测预警配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class MonitorAlarmConfigServiceImpl implements MonitorAlarmConfigService
{
    @Autowired
    private MonitorAlarmConfigMapper monitorAlarmConfigMapper;

    /**
     * 查询监测预警配置
     * 
     * @param id 监测预警配置主键
     * @return 监测预警配置
     */
    @Override
    public MonitorAlarmConfig selectMonitorAlarmConfigById(Long id)
    {
        return monitorAlarmConfigMapper.selectMonitorAlarmConfigById(id);
    }

    /**
     * 查询监测预警配置列表
     * 
     * @param monitorAlarmConfig 监测预警配置
     * @return 监测预警配置
     */
    @Override
    public List<MonitorAlarmConfig> selectMonitorAlarmConfigList(MonitorAlarmConfig monitorAlarmConfig)
    {
        return monitorAlarmConfigMapper.selectMonitorAlarmConfigList(monitorAlarmConfig);
    }

    /**
     * 新增监测预警配置
     * 
     * @param monitorAlarmConfig 监测预警配置
     * @return 结果
     */
    @Override
    public int insertMonitorAlarmConfig(MonitorAlarmConfig monitorAlarmConfig)
    {
        monitorAlarmConfig.setCreateTime(DateUtils.getNowDate());
        return monitorAlarmConfigMapper.insertMonitorAlarmConfig(monitorAlarmConfig);
    }

    /**
     * 修改监测预警配置
     * 
     * @param monitorAlarmConfig 监测预警配置
     * @return 结果
     */
    @Override
    public int updateMonitorAlarmConfig(MonitorAlarmConfig monitorAlarmConfig)
    {
        monitorAlarmConfig.setUpdateTime(DateUtils.getNowDate());
        return monitorAlarmConfigMapper.updateMonitorAlarmConfig(monitorAlarmConfig);
    }

    /**
     * 批量删除监测预警配置
     * 
     * @param ids 需要删除的监测预警配置主键
     * @return 结果
     */
    @Override
    public int deleteMonitorAlarmConfigByIds(Long[] ids)
    {
        return monitorAlarmConfigMapper.deleteMonitorAlarmConfigByIds(ids);
    }

    /**
     * 删除监测预警配置信息
     * 
     * @param id 监测预警配置主键
     * @return 结果
     */
    @Override
    public int deleteMonitorAlarmConfigById(Long id)
    {
        return monitorAlarmConfigMapper.deleteMonitorAlarmConfigById(id);
    }
}

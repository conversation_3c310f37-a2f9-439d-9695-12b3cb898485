package com.tunnel.service.impl;

import com.tunnel.common.core.text.Convert;
import com.tunnel.domain.RequirementStatus;
import com.tunnel.mapper.RequirementStatusMapper;
import com.tunnel.service.RequirementStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备状态记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class RequirementStatusServiceImpl implements RequirementStatusService
{
    @Autowired
    private RequirementStatusMapper requirementStatusMapper;

    /**
     * 查询设备状态记录
     * 
     * @param id 设备状态记录主键
     * @return 设备状态记录
     */
    @Override
    public RequirementStatus selectRequirementStatusById(String id)
    {
        return requirementStatusMapper.selectRequirementStatusById(id);
    }

    /**
     * 查询设备状态记录列表
     * 
     * @param requirementStatus 设备状态记录
     * @return 设备状态记录
     */
    @Override
    public List<RequirementStatus> selectRequirementStatusList(RequirementStatus requirementStatus)
    {
        return requirementStatusMapper.selectRequirementStatusList(requirementStatus);
    }

    /**
     * 新增设备状态记录
     * 
     * @param requirementStatus 设备状态记录
     * @return 结果
     */
    @Override
    public int insertRequirementStatus(RequirementStatus requirementStatus)
    {
        return requirementStatusMapper.insertRequirementStatus(requirementStatus);
    }

    /**
     * 修改设备状态记录
     * 
     * @param requirementStatus 设备状态记录
     * @return 结果
     */
    @Override
    public int updateRequirementStatus(RequirementStatus requirementStatus)
    {
        return requirementStatusMapper.updateRequirementStatus(requirementStatus);
    }

    /**
     * 批量删除设备状态记录
     * 
     * @param ids 需要删除的设备状态记录主键
     * @return 结果
     */
    @Override
    public int deleteRequirementStatusByIds(String ids)
    {
        return requirementStatusMapper.deleteRequirementStatusByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除设备状态记录信息
     * 
     * @param id 设备状态记录主键
     * @return 结果
     */
    @Override
    public int deleteRequirementStatusById(String id)
    {
        return requirementStatusMapper.deleteRequirementStatusById(id);
    }

    @Override
    public List<RequirementStatus> listByKey(String key) {
        return requirementStatusMapper.listByKey(key);
    }
}

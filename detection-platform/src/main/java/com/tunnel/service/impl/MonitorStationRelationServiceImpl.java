package com.tunnel.service.impl;

import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.MonitorStationRelation;
import com.tunnel.mapper.MonitorStationRelationMapper;
import com.tunnel.service.MonitorStationRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 监测站点和类型关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class MonitorStationRelationServiceImpl implements MonitorStationRelationService
{
    @Autowired
    private MonitorStationRelationMapper monitorStationRelationMapper;

    /**
     * 查询监测站点和类型关联
     * 
     * @param id 监测站点和类型关联主键
     * @return 监测站点和类型关联
     */
    @Override
    public MonitorStationRelation selectMonitorStationRelationById(Long id)
    {
        return monitorStationRelationMapper.selectMonitorStationRelationById(id);
    }

    /**
     * 查询监测站点和类型关联列表
     * 
     * @param monitorStationRelation 监测站点和类型关联
     * @return 监测站点和类型关联
     */
    @Override
    public List<MonitorStationRelation> selectMonitorStationRelationList(MonitorStationRelation monitorStationRelation)
    {
        return monitorStationRelationMapper.selectMonitorStationRelationList(monitorStationRelation);
    }

    /**
     * 新增监测站点和类型关联
     * 
     * @param monitorStationRelation 监测站点和类型关联
     * @return 结果
     */
    @Override
    public int insertMonitorStationRelation(MonitorStationRelation monitorStationRelation)
    {
        monitorStationRelation.setCreateTime(DateUtils.getNowDate());
        return monitorStationRelationMapper.insertMonitorStationRelation(monitorStationRelation);
    }

    /**
     * 修改监测站点和类型关联
     * 
     * @param monitorStationRelation 监测站点和类型关联
     * @return 结果
     */
    @Override
    public int updateMonitorStationRelation(MonitorStationRelation monitorStationRelation)
    {
        monitorStationRelation.setUpdateTime(DateUtils.getNowDate());
        return monitorStationRelationMapper.updateMonitorStationRelation(monitorStationRelation);
    }

    /**
     * 批量删除监测站点和类型关联
     * 
     * @param ids 需要删除的监测站点和类型关联主键
     * @return 结果
     */
    @Override
    public int deleteMonitorStationRelationByIds(Long[] ids)
    {
        return monitorStationRelationMapper.deleteMonitorStationRelationByIds(ids);
    }

    /**
     * 删除监测站点和类型关联信息
     * 
     * @param id 监测站点和类型关联主键
     * @return 结果
     */
    @Override
    public int deleteMonitorStationRelationById(Long id)
    {
        return monitorStationRelationMapper.deleteMonitorStationRelationById(id);
    }
}

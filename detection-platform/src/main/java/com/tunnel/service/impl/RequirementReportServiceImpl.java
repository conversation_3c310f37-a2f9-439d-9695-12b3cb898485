package com.tunnel.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tunnel.common.core.text.Convert;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.CommonUtil;
import com.tunnel.domain.CheckDTO;
import com.tunnel.domain.RequirementReport;
import com.tunnel.mapper.RequirementReportMapper;
import com.tunnel.mapper.RequirementStatusMapper;
import com.tunnel.mapper.RequirementWarningMapper;
import com.tunnel.service.RequirementReportService;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 设备上报数据记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class RequirementReportServiceImpl implements RequirementReportService {

    private static final Logger log = LoggerFactory.getLogger(RequirementReportServiceImpl.class);

    @Resource
    private RequirementReportMapper requirementReportMapper;
    @Resource
    private RequirementStatusMapper requirementStatusMapper;
    @Resource
    private RequirementWarningMapper requirementWarningMapper;

    /**
     * 查询设备上报数据记录
     *
     * @param id 设备上报数据记录主键
     * @return 设备上报数据记录
     */
    @Override
    public RequirementReport selectRequirementReportById(String id) {
        return requirementReportMapper.selectRequirementReportById(id);
    }

    /**
     * 查询设备上报数据记录列表
     *
     * @param requirementReport 设备上报数据记录
     * @return 设备上报数据记录
     */
    @Override
    public List<RequirementReport> selectRequirementReportList(RequirementReport requirementReport) {
        return requirementReportMapper.selectRequirementReportList(requirementReport);
    }

    /**
     * 新增设备上报数据记录
     *
     * @param requirementReport 设备上报数据记录
     * @return 结果
     */
    @Override
    public int insertRequirementReport(RequirementReport requirementReport) {
        return requirementReportMapper.insertRequirementReport(requirementReport);
    }

    /**
     * 修改设备上报数据记录
     *
     * @param requirementReport 设备上报数据记录
     * @return 结果
     */
    @Override
    public int updateRequirementReport(RequirementReport requirementReport) {
        return requirementReportMapper.updateRequirementReport(requirementReport);
    }

    /**
     * 批量删除设备上报数据记录
     *
     * @param ids 需要删除的设备上报数据记录主键
     * @return 结果
     */
    @Override
    public int deleteRequirementReportByIds(String ids) {
        return requirementReportMapper.deleteRequirementReportByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除设备上报数据记录信息
     *
     * @param id 设备上报数据记录主键
     * @return 结果
     */
    @Override
    public int deleteRequirementReportById(String id) {
        return requirementReportMapper.deleteRequirementReportById(id);
    }

    public List<RequirementReport> listByKey(String key) {
        return requirementReportMapper.listByKey(key);
    }

    @Override
    public List<Map<String, Object>> getCheckFieldsByType(Integer type) {
        log.info("获取检查字段信息，类型: {}", type);

        if (type == null) {
            log.warn("监测类型参数为空");
            return Collections.emptyList();
        }

        List<Map<String, Object>> resultList;
        try {
            if (Objects.equals(type, 1)) {
                // 上报数据
                log.debug("查询上报数据表字段信息");
                resultList = requirementReportMapper.getAllFields();
            } else if (Objects.equals(type, 2)) {
                // 状态数据
                log.debug("查询状态数据表字段信息");
                resultList = requirementStatusMapper.getAllFields();
            } else if (Objects.equals(type, 3)) {
                // 报警数据
                log.debug("查询报警数据表字段信息");
                resultList = requirementWarningMapper.getAllFields();
            } else {
                log.warn("不支持的监测类型: {}", type);
                return Collections.emptyList();
            }

            if (CollectionUtils.isEmpty(resultList)) {
                log.warn("未查询到类型 {} 对应的字段信息", type);
                return Collections.emptyList();
            }

            log.info("成功获取类型 {} 的字段信息，共 {} 个字段", type, resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("获取类型 {} 的字段信息时发生异常", type, e);
            return Collections.emptyList();
        }
    }

    @Override
    public PageInfo<Map<String, Object>> selectCheckListByPage(CheckDTO dto) {
        if (Objects.isNull(dto.getStartTime()) || Objects.isNull(dto.getEndTime())) {
            throw new ServiceException("时间参数不能为空");
        }
        List<Map<String, Object>> resultList = Lists.newArrayList();
        Page page= null;
        if(dto.isPage()){
            page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        }
        if (Objects.equals(dto.getType(), 1)) {
            //查询上报
            resultList = requirementReportMapper.selectByParams(dto);
        }else if (Objects.equals(dto.getType(), 2)) {
            //查询状态
            resultList = requirementStatusMapper.selectByParams(dto);
        }else if (Objects.equals(dto.getType(), 3)) {
            //查询报警
            resultList = requirementWarningMapper.selectByParams(dto);
        }
        //返回结果
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(resultList);
        if(dto.isPage()){
            pageInfo.setTotal(page.getTotal());
        }
        return pageInfo;
    }

    @Override
    public Map<String, Object> selectCheckListForChart(CheckDTO dto) {
        dto.setPage(false);
        PageInfo<Map<String, Object>> pageInfo = this.selectCheckListByPage(dto);
        List<Map<String, Object>> resultList = pageInfo.getList();
        if (CollectionUtils.isEmpty(resultList)) {
            return new HashMap<>();
        }
        // 获取第一条记录的所有字段名（排除因子相关的字段）
        Set<String> fieldNames = new HashSet<>();
        for (Map<String, Object> record : resultList) {
            if (record == null) {
                continue;
            }
            for (String key : record.keySet()) {
                if (key == null) {
                    continue;
                }
                // 排除因子相关的字段（以_factor, _name, _unit等结尾的字段）
                if (!key.equals("id") && !key.equals("monitor_code") &&
                        !key.equals("b_key") && !key.equals("topic") &&
                        !key.equals("remark") && !key.equals("create_time") &&
                        !key.equals("update_time") && !key.equals("creator")
                        && !key.equals("modifier")) {
                    fieldNames.add(key);
                }
            }
        }
        // 为每个字段创建一个数据集合
        Map<String, Object> chartData = new HashMap<>();
        for (String fieldName : fieldNames) {
            List<Object> fieldValues = new ArrayList<>();
            List<String> timeLabels = new ArrayList<>();
            for (Map<String, Object> record : resultList) {
                if (record == null) {
                    continue;
                }
                // 添加时间标签（假设有create_time字段）
                if (record.containsKey("create_time")) {
                    Object timeValue = record.get("create_time");
                    if (timeValue != null) {
                        String timeStr = CommonUtil.formatTimeValue(timeValue);
                        timeLabels.add(timeStr);
                    }
                }
                // 添加字段值
                Object value = record.get(fieldName);
                fieldValues.add(value);
            }
            // 将字段数据和时间标签一起存储
            Map<String, Object> fieldData = new HashMap<>();
            fieldData.put("values", fieldValues);
            fieldData.put("times", timeLabels);
            chartData.put(fieldName, fieldData);
        }
        return chartData;
    }

    @Override
    public CheckDTO selectTimeRange(CheckDTO dto) {
        if(Objects.isNull(dto.getType())){
            throw new ServiceException("请选择监测类型");
        }
        if(Objects.isNull(dto.getGatewayId())){
            throw new ServiceException("请选择网关");
        }
        CheckDTO result = new CheckDTO();
        if(Objects.equals(dto.getType(), 1)){
            //查询上报
            result = requirementReportMapper.selectTimeRange(dto);
        } else if (Objects.equals(dto.getType(), 2)) {
            //查询状态
            result = requirementStatusMapper.selectTimeRange(dto);
        } else if (Objects.equals(dto.getType(), 3)) {
            //查询报警
            result = requirementWarningMapper.selectTimeRange(dto);
        }
        if(Objects.nonNull(result) && Objects.nonNull(result.getEndTime())){
            Date endTime = result.getEndTime();
            //根据endTime 转换成当天0点的时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endTime);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date dayStartTime = calendar.getTime();
            result.setStartTime(dayStartTime);
        }
        return result;
    }
}

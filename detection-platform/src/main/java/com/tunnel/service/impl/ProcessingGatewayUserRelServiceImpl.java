package com.tunnel.service.impl;

import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.ProcessingGatewayUserRel;
import com.tunnel.mapper.ProcessingGatewayUserRelMapper;
import com.tunnel.service.ProcessingGatewayUserRelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 控制网关和用户关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class ProcessingGatewayUserRelServiceImpl implements ProcessingGatewayUserRelService
{
    @Autowired
    private ProcessingGatewayUserRelMapper processingGatewayUserRelMapper;

    /**
     * 查询控制网关和用户关联
     * 
     * @param id 控制网关和用户关联主键
     * @return 控制网关和用户关联
     */
    @Override
    public ProcessingGatewayUserRel selectProcessingGatewayUserRelById(Long id)
    {
        return processingGatewayUserRelMapper.selectProcessingGatewayUserRelById(id);
    }

    /**
     * 查询控制网关和用户关联列表
     * 
     * @param processingGatewayUserRel 控制网关和用户关联
     * @return 控制网关和用户关联
     */
    @Override
    public List<ProcessingGatewayUserRel> selectProcessingGatewayUserRelList(ProcessingGatewayUserRel processingGatewayUserRel)
    {
        return processingGatewayUserRelMapper.selectProcessingGatewayUserRelList(processingGatewayUserRel);
    }

    /**
     * 新增控制网关和用户关联
     * 
     * @param processingGatewayUserRel 控制网关和用户关联
     * @return 结果
     */
    @Override
    public int insertProcessingGatewayUserRel(ProcessingGatewayUserRel processingGatewayUserRel)
    {
        processingGatewayUserRel.setCreateTime(DateUtils.getNowDate());
        return processingGatewayUserRelMapper.insertProcessingGatewayUserRel(processingGatewayUserRel);
    }

    /**
     * 修改控制网关和用户关联
     * 
     * @param processingGatewayUserRel 控制网关和用户关联
     * @return 结果
     */
    @Override
    public int updateProcessingGatewayUserRel(ProcessingGatewayUserRel processingGatewayUserRel)
    {
        processingGatewayUserRel.setUpdateTime(DateUtils.getNowDate());
        return processingGatewayUserRelMapper.updateProcessingGatewayUserRel(processingGatewayUserRel);
    }

    /**
     * 批量删除控制网关和用户关联
     * 
     * @param ids 需要删除的控制网关和用户关联主键
     * @return 结果
     */
    @Override
    public int deleteProcessingGatewayUserRelByIds(Long[] ids)
    {
        return processingGatewayUserRelMapper.deleteProcessingGatewayUserRelByIds(ids);
    }

    /**
     * 删除控制网关和用户关联信息
     * 
     * @param id 控制网关和用户关联主键
     * @return 结果
     */
    @Override
    public int deleteProcessingGatewayUserRelById(Long id)
    {
        return processingGatewayUserRelMapper.deleteProcessingGatewayUserRelById(id);
    }
}

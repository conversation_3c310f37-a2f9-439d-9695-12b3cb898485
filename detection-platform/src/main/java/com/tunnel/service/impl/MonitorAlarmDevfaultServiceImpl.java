package com.tunnel.service.impl;

import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.MonitorAlarmDevfault;
import com.tunnel.mapper.MonitorAlarmDevfaultMapper;
import com.tunnel.service.MonitorAlarmDevfaultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备故障报警Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class MonitorAlarmDevfaultServiceImpl implements MonitorAlarmDevfaultService
{
    @Autowired
    private MonitorAlarmDevfaultMapper monitorAlarmDevfaultMapper;

    /**
     * 查询设备故障报警
     * 
     * @param id 设备故障报警主键
     * @return 设备故障报警
     */
    @Override
    public MonitorAlarmDevfault selectMonitorAlarmDevfaultById(Long id)
    {
        return monitorAlarmDevfaultMapper.selectMonitorAlarmDevfaultById(id);
    }

    /**
     * 查询设备故障报警列表
     * 
     * @param monitorAlarmDevfault 设备故障报警
     * @return 设备故障报警
     */
    @Override
    public List<MonitorAlarmDevfault> selectMonitorAlarmDevfaultList(MonitorAlarmDevfault monitorAlarmDevfault)
    {
        return monitorAlarmDevfaultMapper.selectMonitorAlarmDevfaultList(monitorAlarmDevfault);
    }

    /**
     * 新增设备故障报警
     * 
     * @param monitorAlarmDevfault 设备故障报警
     * @return 结果
     */
    @Override
    public int insertMonitorAlarmDevfault(MonitorAlarmDevfault monitorAlarmDevfault)
    {
        monitorAlarmDevfault.setCreateTime(DateUtils.getNowDate());
        return monitorAlarmDevfaultMapper.insertMonitorAlarmDevfault(monitorAlarmDevfault);
    }

    /**
     * 修改设备故障报警
     * 
     * @param monitorAlarmDevfault 设备故障报警
     * @return 结果
     */
    @Override
    public int updateMonitorAlarmDevfault(MonitorAlarmDevfault monitorAlarmDevfault)
    {
        monitorAlarmDevfault.setUpdateTime(DateUtils.getNowDate());
        return monitorAlarmDevfaultMapper.updateMonitorAlarmDevfault(monitorAlarmDevfault);
    }

    /**
     * 批量删除设备故障报警
     * 
     * @param ids 需要删除的设备故障报警主键
     * @return 结果
     */
    @Override
    public int deleteMonitorAlarmDevfaultByIds(Long[] ids)
    {
        return monitorAlarmDevfaultMapper.deleteMonitorAlarmDevfaultByIds(ids);
    }

    /**
     * 删除设备故障报警信息
     * 
     * @param id 设备故障报警主键
     * @return 结果
     */
    @Override
    public int deleteMonitorAlarmDevfaultById(Long id)
    {
        return monitorAlarmDevfaultMapper.deleteMonitorAlarmDevfaultById(id);
    }
}

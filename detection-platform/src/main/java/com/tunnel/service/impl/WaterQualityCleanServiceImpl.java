package com.tunnel.service.impl;

import cn.hutool.core.date.DateUtil;
import com.tunnel.domain.MonitorSewage;
import com.tunnel.mapper.MonitorFactorMapper;
import com.tunnel.mapper.MonitorSewageMapper;
import com.tunnel.service.HistoryDataAccessService;
import com.tunnel.service.WaterQualityCleanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 水质监测数据清洗服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class WaterQualityCleanServiceImpl implements WaterQualityCleanService {

    private static final int BATCH_SIZE = 1000;
    private static final Logger log = LoggerFactory.getLogger(WaterQualityCleanServiceImpl.class);

    @Resource
    private HistoryDataAccessService historyDataAccessService;
    @Resource
    private MonitorFactorMapper monitorFactorMapper;
    @Resource
    private MonitorSewageMapper monitorSewageMapper;

    @Override
    public void cleanRange(String fromYyyyMM, String toYyyyMM) {
        YearMonth start = YearMonth.parse(fromYyyyMM, DateTimeFormatter.ofPattern("yyyyMM"));
        YearMonth end = YearMonth.parse(toYyyyMM, DateTimeFormatter.ofPattern("yyyyMM"));
        YearMonth cur = start;
        while (!cur.isAfter(end)) {
            cleanMonth(cur.format(DateTimeFormatter.ofPattern("yyyyMM")));
            cur = cur.plusMonths(1);
        }
    }

    @Override
    public void cleanMonth(String yyyyMM) {
        String table = "history_data_" + yyyyMM;
        log.info("[cleanMonth] start, month={}, table={}, batchSize={}", yyyyMM, table, BATCH_SIZE);
        long startTs = System.currentTimeMillis();
        long totalTimeKeys = 0L;
        long totalRows = 0L;
        int batchIndex = 0;
        String lastTime = null;

        while (true) {
            batchIndex++;
            // 1) 先取一批去重后的时间点，保证同一时间点不会被截断
            log.info("[cleanMonth] batch#{} fetch distinct times, lastTime={}, limit={}", batchIndex, lastTime, BATCH_SIZE);
            List<String> times = historyDataAccessService.selectDistinctTimes(table, lastTime, BATCH_SIZE);
            if (times == null || times.isEmpty()) {
                log.info("[cleanMonth] batch#{} no more times, exiting loop", batchIndex);
                break;
            }
            totalTimeKeys += times.size();
            String firstTime = times.get(0);
            String lastTimeInBatch = times.get(times.size() - 1);
            log.info("[cleanMonth] batch#{} got {} times, range {} .. {}", batchIndex, times.size(), firstTime, lastTimeInBatch);

            // 2) 基于这些时间点，查询完整明细
            long t2 = System.currentTimeMillis();
            List<Map<String, Object>> rows = historyDataAccessService.selectByTimes(table, times);
            if (rows == null || rows.isEmpty()) {
                lastTime = times.get(times.size() - 1);
                log.warn("[cleanMonth] batch#{} no rows returned for {} times, advance lastTime to {}", batchIndex, times.size(), lastTime);
                continue;
            }
            totalRows += rows.size();
            log.info("[cleanMonth] batch#{} fetched {} rows for {} times ({} ms)", batchIndex, rows.size(), times.size(), (System.currentTimeMillis()-t2));

            // 3) 按时间分组并转横
            Map<String, List<Map<String, Object>>> grouped = rows.stream().collect(Collectors.groupingBy(row -> (String) row.get("time")));
            List<MonitorSewage> monitorDataList = new ArrayList<>();
            log.info("[cleanMonth] batch#{} grouping done, {} groups", batchIndex, grouped.size());

            for (Map.Entry<String, List<Map<String, Object>>> entry : grouped.entrySet()) {
                String dateTime = entry.getKey();
                List<Map<String, Object>> list = entry.getValue();

                MonitorSewage monitorData = new MonitorSewage();
                monitorData.setTopic(null);
                monitorData.setBKey(dateTime);
                Date date = DateUtil.parseDateTime(dateTime);
                monitorData.setCreateTime(date);

                // 处理每个时间点的数据
                for (Map<String, Object> row : list) {
                    String factorCode = (String) row.get("factor_code");
                    String factorName = (String) row.get("factor_name");
                    String systemCode = (String) row.get("monitoringSystem_systemCode");
                    Object value = row.get("value");
                    
                    if (factorCode != null) {
                        String valueStr = value == null ? null : value.toString();
                        setFieldValue(monitorData, factorCode, valueStr);
                        
                        // 设置监测系统编码
                        if (monitorData.getMonitorCode() == null) {
                            monitorData.setMonitorCode(systemCode);
                        }
                    }
                }

                monitorDataList.add(monitorData);
            }

            if (!monitorDataList.isEmpty()) {
                monitorSewageMapper.insertBatch(monitorDataList);
            }
            log.info("[cleanMonth] batch#{} inserted: monitorData={}", batchIndex, monitorDataList.size());

            // 4) 标记已处理：按时间点整体标记，避免跨批遗漏
            historyDataAccessService.markProcessedByTimes(table, times);
            log.info("[cleanMonth] batch#{} marked processed for {} times ({} .. {})", batchIndex, times.size(), firstTime, lastTimeInBatch);

            // 5) 推进游标
            lastTime = times.get(times.size() - 1);
        }
        log.info("[cleanMonth] done, month={}, table={}, totalTimeKeys={}, totalRows={}, elapsedMs={}", yyyyMM, table, totalTimeKeys, totalRows, (System.currentTimeMillis()-startTs));
    }


    /**
     * 设置字段值
     */
    private void setFieldValue(MonitorSewage monitorData, String fieldName, String value) {
        if (fieldName == null) return;
        
        try {
            // 根据字段名设置对应的属性
            switch (fieldName.toLowerCase()) {
                case "ph":
                    monitorData.setPh(value);
                    break;
                case "wt":
                    monitorData.setWt(value);
                    break;
                case "cod":
                    monitorData.setCod(value);
                    break;
                case "tn":
                    monitorData.setTn(value);
                    break;
                case "nh3n":
                case "nh3-n":
                    monitorData.setNh3n(value);
                    break;
                case "tp":
                    monitorData.setTp(value);
                    break;
                case "oil":
                    monitorData.setOil(value);
                    break;
                case "ss":
                    monitorData.setSs(value);
                    break;
                case "swtp":
                    monitorData.setSwtp(value);
                    break;
                case "swnh4n":
                case "swnh4-n":
                    monitorData.setSwnh4n(value);
                    break;
                case "swcod":
                    monitorData.setSwcod(value);
                    break;
                case "swoil":
                    monitorData.setSwoil(value);
                    break;
                case "turb":
                    monitorData.setTurb(value);
                    break;
                case "flow":
                    monitorData.setFlow(value);
                    break;
                case "wfloat":
                    monitorData.setWfloat(value);
                    break;
                case "wrtflo":
                    monitorData.setWrtflo(value);
                    break;
                case "hight":
                    monitorData.setHight(value);
                    break;
                default:
                    log.debug("Unknown field: {}", fieldName);
                    break;
            }
        } catch (Exception e) {
            log.warn("字段映射失败Failed to set field {} with value {}", fieldName, value, e);
        }
    }
}

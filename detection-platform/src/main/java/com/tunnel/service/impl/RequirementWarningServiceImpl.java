package com.tunnel.service.impl;

import com.tunnel.common.core.text.Convert;
import com.tunnel.domain.RequirementWarning;
import com.tunnel.mapper.RequirementWarningMapper;
import com.tunnel.service.RequirementWarningService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备报警记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class RequirementWarningServiceImpl implements RequirementWarningService
{
    @Autowired
    private RequirementWarningMapper requirementWarningMapper;

    /**
     * 查询设备报警记录
     * 
     * @param id 设备报警记录主键
     * @return 设备报警记录
     */
    @Override
    public RequirementWarning selectRequirementWarningById(String id)
    {
        return requirementWarningMapper.selectRequirementWarningById(id);
    }

    /**
     * 查询设备报警记录列表
     * 
     * @param requirementWarning 设备报警记录
     * @return 设备报警记录
     */
    @Override
    public List<RequirementWarning> selectRequirementWarningList(RequirementWarning requirementWarning)
    {
        return requirementWarningMapper.selectRequirementWarningList(requirementWarning);
    }

    /**
     * 新增设备报警记录
     * 
     * @param requirementWarning 设备报警记录
     * @return 结果
     */
    @Override
    public int insertRequirementWarning(RequirementWarning requirementWarning)
    {
        return requirementWarningMapper.insertRequirementWarning(requirementWarning);
    }

    /**
     * 修改设备报警记录
     * 
     * @param requirementWarning 设备报警记录
     * @return 结果
     */
    @Override
    public int updateRequirementWarning(RequirementWarning requirementWarning)
    {
        return requirementWarningMapper.updateRequirementWarning(requirementWarning);
    }

    /**
     * 批量删除设备报警记录
     * 
     * @param ids 需要删除的设备报警记录主键
     * @return 结果
     */
    @Override
    public int deleteRequirementWarningByIds(String ids)
    {
        return requirementWarningMapper.deleteRequirementWarningByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除设备报警记录信息
     * 
     * @param id 设备报警记录主键
     * @return 结果
     */
    @Override
    public int deleteRequirementWarningById(String id)
    {
        return requirementWarningMapper.deleteRequirementWarningById(id);
    }

    public List<RequirementWarning> listByKey(String key)
    {
        return requirementWarningMapper.listByKey(key);
    }
}

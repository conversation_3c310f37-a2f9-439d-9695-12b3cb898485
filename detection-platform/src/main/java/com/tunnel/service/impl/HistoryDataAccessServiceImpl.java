package com.tunnel.service.impl;

import com.tunnel.common.annotation.DataSource;
import com.tunnel.common.enums.DataSourceType;
import com.tunnel.mapper.HistoryDataMapper;
import com.tunnel.service.HistoryDataAccessService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 历史数据访问服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class HistoryDataAccessServiceImpl implements HistoryDataAccessService {

    @Resource
    private HistoryDataMapper historyDataMapper;

    @Override
    @DataSource(DataSourceType.SLAVE1)
    public List<String> selectDistinctTimes(String tableName, String lastTime, int limitSize) {
        return historyDataMapper.selectDistinctTimes(tableName, lastTime, limitSize);
    }

    @Override
    @DataSource(DataSourceType.SLAVE1)
    public List<Map<String, Object>> selectByTimes(String tableName, List<String> times) {
        return historyDataMapper.selectByTimes(tableName, times);
    }

    @Override
    @DataSource(DataSourceType.SLAVE1)
    public void markProcessedByTimes(String tableName, List<String> times) {
        historyDataMapper.markProcessedByTimes(tableName, times);
    }
}

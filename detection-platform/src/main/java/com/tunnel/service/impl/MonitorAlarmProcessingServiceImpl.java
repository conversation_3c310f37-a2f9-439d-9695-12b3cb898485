package com.tunnel.service.impl;

import java.util.List;
import com.tunnel.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tunnel.mapper.MonitorAlarmProcessingMapper;
import com.tunnel.domain.MonitorAlarmProcessing;
import com.tunnel.service.MonitorAlarmProcessingService;

/**
 * 监测指标报警Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class MonitorAlarmProcessingServiceImpl implements MonitorAlarmProcessingService
{
    @Autowired
    private MonitorAlarmProcessingMapper monitorAlarmProcessingMapper;

    /**
     * 查询监测指标报警
     * 
     * @param id 监测指标报警主键
     * @return 监测指标报警
     */
    @Override
    public MonitorAlarmProcessing selectMonitorAlarmProcessingById(Long id)
    {
        return monitorAlarmProcessingMapper.selectMonitorAlarmProcessingById(id);
    }

    /**
     * 查询监测指标报警列表
     * 
     * @param monitorAlarmProcessing 监测指标报警
     * @return 监测指标报警
     */
    @Override
    public List<MonitorAlarmProcessing> selectMonitorAlarmProcessingList(MonitorAlarmProcessing monitorAlarmProcessing)
    {
        return monitorAlarmProcessingMapper.selectMonitorAlarmProcessingList(monitorAlarmProcessing);
    }

    /**
     * 新增监测指标报警
     * 
     * @param monitorAlarmProcessing 监测指标报警
     * @return 结果
     */
    @Override
    public int insertMonitorAlarmProcessing(MonitorAlarmProcessing monitorAlarmProcessing)
    {
        monitorAlarmProcessing.setCreateTime(DateUtils.getNowDate());
        return monitorAlarmProcessingMapper.insertMonitorAlarmProcessing(monitorAlarmProcessing);
    }

    /**
     * 修改监测指标报警
     * 
     * @param monitorAlarmProcessing 监测指标报警
     * @return 结果
     */
    @Override
    public int updateMonitorAlarmProcessing(MonitorAlarmProcessing monitorAlarmProcessing)
    {
        monitorAlarmProcessing.setUpdateTime(DateUtils.getNowDate());
        return monitorAlarmProcessingMapper.updateMonitorAlarmProcessing(monitorAlarmProcessing);
    }

    /**
     * 批量删除监测指标报警
     * 
     * @param ids 需要删除的监测指标报警主键
     * @return 结果
     */
    @Override
    public int deleteMonitorAlarmProcessingByIds(Long[] ids)
    {
        return monitorAlarmProcessingMapper.deleteMonitorAlarmProcessingByIds(ids);
    }

    /**
     * 删除监测指标报警信息
     * 
     * @param id 监测指标报警主键
     * @return 结果
     */
    @Override
    public int deleteMonitorAlarmProcessingById(Long id)
    {
        return monitorAlarmProcessingMapper.deleteMonitorAlarmProcessingById(id);
    }
}

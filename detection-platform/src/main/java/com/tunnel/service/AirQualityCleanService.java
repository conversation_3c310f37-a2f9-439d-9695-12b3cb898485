package com.tunnel.service;

/**
 * 空气监测数据清洗服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface AirQualityCleanService {
    
    /**
     * 清洗指定时间范围的数据
     * 
     * @param fromYyyyMM 开始时间 (格式: yyyyMM)
     * @param toYyyyMM 结束时间 (格式: yyyyMM)
     */
    void cleanRange(String fromYyyyMM, String toYyyyMM);
    
    /**
     * 清洗指定月份的数据
     * 
     * @param yyyyMM 月份 (格式: yyyyMM)
     */
    void cleanMonth(String yyyyMM);
}

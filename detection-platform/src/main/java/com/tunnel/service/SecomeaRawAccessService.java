package com.tunnel.service;


import com.tunnel.domain.SecomeaRawRow;

import java.util.List;

public interface SecomeaRawAccessService {

    List<SecomeaRawRow> selectBatch(String tableName, int limitSize);

    void markProcessed(String tableName, List<String> ids);


    List<String> selectDistinctTimes(String tableName, String lastTime, int limitSize);

    List<SecomeaRawRow> selectByTimes(String tableName, List<String> times);

    void markProcessedByTimes(String tableName, List<String> times);
}



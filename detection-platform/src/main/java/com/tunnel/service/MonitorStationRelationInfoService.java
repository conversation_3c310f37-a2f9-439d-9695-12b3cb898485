package com.tunnel.service;

import com.tunnel.domain.MonitorStationRelationInfo;

import java.util.List;

/**
 * 监测站点详细信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface MonitorStationRelationInfoService
{
    /**
     * 查询监测站点详细信息
     * 
     * @param id 监测站点详细信息主键
     * @return 监测站点详细信息
     */
    public MonitorStationRelationInfo selectMonitorStationRelationInfoById(Long id);

    /**
     * 查询监测站点详细信息列表
     * 
     * @param monitorStationRelationInfo 监测站点详细信息
     * @return 监测站点详细信息集合
     */
    public List<MonitorStationRelationInfo> selectMonitorStationRelationInfoList(MonitorStationRelationInfo monitorStationRelationInfo);

    /**
     * 新增监测站点详细信息
     * 
     * @param monitorStationRelationInfo 监测站点详细信息
     * @return 结果
     */
    public int insertMonitorStationRelationInfo(MonitorStationRelationInfo monitorStationRelationInfo);

    /**
     * 修改监测站点详细信息
     * 
     * @param monitorStationRelationInfo 监测站点详细信息
     * @return 结果
     */
    public int updateMonitorStationRelationInfo(MonitorStationRelationInfo monitorStationRelationInfo);

    /**
     * 批量删除监测站点详细信息
     * 
     * @param ids 需要删除的监测站点详细信息主键集合
     * @return 结果
     */
    public int deleteMonitorStationRelationInfoByIds(Long[] ids);

    /**
     * 删除监测站点详细信息信息
     * 
     * @param id 监测站点详细信息主键
     * @return 结果
     */
    public int deleteMonitorStationRelationInfoById(Long id);
}

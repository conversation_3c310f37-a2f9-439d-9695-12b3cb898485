package com.tunnel.service;

import com.tunnel.domain.DailyReport;

import java.util.List;

/**
 * 日报Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface DailyReportService
{
    /**
     * 查询日报
     * 
     * @param id 日报主键
     * @return 日报
     */
    public DailyReport selectDailyReportById(Long id);

    /**
     * 查询日报列表
     * 
     * @param dailyReport 日报
     * @return 日报集合
     */
    public List<DailyReport> selectDailyReportList(DailyReport dailyReport);

    /**
     * 新增日报
     * 
     * @param dailyReport 日报
     * @return 结果
     */
    public int insertDailyReport(DailyReport dailyReport);

    /**
     * 修改日报
     * 
     * @param dailyReport 日报
     * @return 结果
     */
    public int updateDailyReport(DailyReport dailyReport);

    /**
     * 批量删除日报
     * 
     * @param ids 需要删除的日报主键集合
     * @return 结果
     */
    public int deleteDailyReportByIds(Long[] ids);

    /**
     * 删除日报信息
     * 
     * @param id 日报主键
     * @return 结果
     */
    public int deleteDailyReportById(Long id);
}

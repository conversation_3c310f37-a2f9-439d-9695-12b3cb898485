package com.tunnel.service;

import com.tunnel.domain.MonitorFactor;

import java.util.List;

/**
 * 监测因子Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface MonitorFactorService
{
    /**
     * 查询监测因子
     * 
     * @param id 监测因子主键
     * @return 监测因子
     */
    public MonitorFactor selectMonitorFactorById(Long id);

    /**
     * 查询监测因子列表
     * 
     * @param monitorFactor 监测因子
     * @return 监测因子集合
     */
    public List<MonitorFactor> selectMonitorFactorList(MonitorFactor monitorFactor);

    /**
     * 新增监测因子
     * 
     * @param monitorFactor 监测因子
     * @return 结果
     */
    public int insertMonitorFactor(MonitorFactor monitorFactor);

    /**
     * 修改监测因子
     * 
     * @param monitorFactor 监测因子
     * @return 结果
     */
    public int updateMonitorFactor(MonitorFactor monitorFactor);

    /**
     * 批量删除监测因子
     * 
     * @param ids 需要删除的监测因子主键集合
     * @return 结果
     */
    public int deleteMonitorFactorByIds(Long[] ids);

    /**
     * 删除监测因子信息
     * 
     * @param id 监测因子主键
     * @return 结果
     */
    public int deleteMonitorFactorById(Long id);
}

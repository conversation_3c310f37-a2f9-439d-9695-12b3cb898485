package com.tunnel.service;

import com.tunnel.domain.ScRequirement;

import java.util.List;

/**
 * 设备状态信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface ScRequirementService
{
    /**
     * 查询设备信息
     * 
     * @param id 设备信息主键
     * @return 设备信息
     */
    public ScRequirement selectScRequirementById(Long id);

    /**
     * 查询设备信息列表
     * 
     * @param scRequirement 设备信息
     * @return 设备信息集合
     */
    public List<ScRequirement> selectScRequirementList(ScRequirement scRequirement);

    /**
     * 新增设备信息
     * 
     * @param scRequirement 设备信息
     * @return 结果
     */
    public int insertScRequirement(ScRequirement scRequirement);

    /**
     * 修改设备信息
     * 
     * @param scRequirement 设备信息
     * @return 结果
     */
    public int updateScRequirement(ScRequirement scRequirement);

    /**
     * 批量删除设备信息
     * 
     * @param ids 需要删除的设备信息主键集合
     * @return 结果
     */
    public int deleteScRequirementByIds(String[] ids);

    /**
     * 删除设备信息信息
     * 
     * @param id 设备信息主键
     * @return 结果
     */
    public int deleteScRequirementById(String id);
}

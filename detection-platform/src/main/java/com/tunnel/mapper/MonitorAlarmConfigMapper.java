package com.tunnel.mapper;

import com.tunnel.domain.MonitorAlarmConfig;

import java.util.List;

/**
 * 监测预警配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface MonitorAlarmConfigMapper 
{
    /**
     * 查询监测预警配置
     * 
     * @param id 监测预警配置主键
     * @return 监测预警配置
     */
    public MonitorAlarmConfig selectMonitorAlarmConfigById(Long id);

    /**
     * 查询监测预警配置列表
     * 
     * @param monitorAlarmConfig 监测预警配置
     * @return 监测预警配置集合
     */
    public List<MonitorAlarmConfig> selectMonitorAlarmConfigList(MonitorAlarmConfig monitorAlarmConfig);

    /**
     * 新增监测预警配置
     * 
     * @param monitorAlarmConfig 监测预警配置
     * @return 结果
     */
    public int insertMonitorAlarmConfig(MonitorAlarmConfig monitorAlarmConfig);

    /**
     * 修改监测预警配置
     * 
     * @param monitorAlarmConfig 监测预警配置
     * @return 结果
     */
    public int updateMonitorAlarmConfig(MonitorAlarmConfig monitorAlarmConfig);

    /**
     * 删除监测预警配置
     * 
     * @param id 监测预警配置主键
     * @return 结果
     */
    public int deleteMonitorAlarmConfigById(Long id);

    /**
     * 批量删除监测预警配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonitorAlarmConfigByIds(Long[] ids);
}

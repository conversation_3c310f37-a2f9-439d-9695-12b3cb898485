package com.tunnel.mapper;

import com.tunnel.domain.MonitorDTO;
import com.tunnel.domain.MonitorNoise;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 噪声监测数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Mapper
public interface MonitorNoiseMapper {
    /**
     * 查询噪声监测数据
     * 
     * @param id 噪声监测数据主键
     * @return 噪声监测数据
     */
    public MonitorNoise selectScMonitorNoiseById(Long id);

    /**
     * 查询噪声监测数据列表
     * 
     * @param monitorNoise 噪声监测数据
     * @return 噪声监测数据集合
     */
    public List<MonitorNoise> selectScMonitorNoiseList(MonitorNoise monitorNoise);

    /**
     * 新增噪声监测数据
     * 
     * @param monitorNoise 噪声监测数据
     * @return 结果
     */
    public int insertScMonitorNoise(MonitorNoise monitorNoise);

    /**
     * 修改噪声监测数据
     * 
     * @param monitorNoise 噪声监测数据
     * @return 结果
     */
    public int updateScMonitorNoise(MonitorNoise monitorNoise);

    /**
     * 删除噪声监测数据
     * 
     * @param id 噪声监测数据主键
     * @return 结果
     */
    public int deleteScMonitorNoiseById(Long id);

    /**
     * 批量删除噪声监测数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScMonitorNoiseByIds(Long[] ids);

    /**
     * 批量插入噪声监测数据
     * 
     * @param list 数据列表
     * @return 结果
     */
    public int insertBatch(@Param("list") List<MonitorNoise> list);

    List<Map<String, Object>> selectByParams(MonitorDTO dto);

    MonitorDTO selectTimeRange(MonitorDTO dto);
}

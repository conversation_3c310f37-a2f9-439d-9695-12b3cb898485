package com.tunnel.mapper;

import com.tunnel.domain.ProcessingGatewayUserRel;

import java.util.List;

/**
 * 控制网关和用户关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface ProcessingGatewayUserRelMapper 
{
    /**
     * 查询控制网关和用户关联
     * 
     * @param id 控制网关和用户关联主键
     * @return 控制网关和用户关联
     */
    public ProcessingGatewayUserRel selectProcessingGatewayUserRelById(Long id);

    /**
     * 查询控制网关和用户关联列表
     * 
     * @param processingGatewayUserRel 控制网关和用户关联
     * @return 控制网关和用户关联集合
     */
    public List<ProcessingGatewayUserRel> selectProcessingGatewayUserRelList(ProcessingGatewayUserRel processingGatewayUserRel);

    /**
     * 新增控制网关和用户关联
     * 
     * @param processingGatewayUserRel 控制网关和用户关联
     * @return 结果
     */
    public int insertProcessingGatewayUserRel(ProcessingGatewayUserRel processingGatewayUserRel);

    /**
     * 修改控制网关和用户关联
     * 
     * @param processingGatewayUserRel 控制网关和用户关联
     * @return 结果
     */
    public int updateProcessingGatewayUserRel(ProcessingGatewayUserRel processingGatewayUserRel);

    /**
     * 删除控制网关和用户关联
     * 
     * @param id 控制网关和用户关联主键
     * @return 结果
     */
    public int deleteProcessingGatewayUserRelById(Long id);

    /**
     * 批量删除控制网关和用户关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProcessingGatewayUserRelByIds(Long[] ids);
}

package com.tunnel.mapper;

import com.tunnel.domain.MonitorStationRelation;

import java.util.List;

/**
 * 监测站点和类型关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface MonitorStationRelationMapper 
{
    /**
     * 查询监测站点和类型关联
     * 
     * @param id 监测站点和类型关联主键
     * @return 监测站点和类型关联
     */
    public MonitorStationRelation selectMonitorStationRelationById(Long id);

    /**
     * 查询监测站点和类型关联列表
     * 
     * @param monitorStationRelation 监测站点和类型关联
     * @return 监测站点和类型关联集合
     */
    public List<MonitorStationRelation> selectMonitorStationRelationList(MonitorStationRelation monitorStationRelation);

    /**
     * 新增监测站点和类型关联
     * 
     * @param monitorStationRelation 监测站点和类型关联
     * @return 结果
     */
    public int insertMonitorStationRelation(MonitorStationRelation monitorStationRelation);

    /**
     * 修改监测站点和类型关联
     * 
     * @param monitorStationRelation 监测站点和类型关联
     * @return 结果
     */
    public int updateMonitorStationRelation(MonitorStationRelation monitorStationRelation);

    /**
     * 删除监测站点和类型关联
     * 
     * @param id 监测站点和类型关联主键
     * @return 结果
     */
    public int deleteMonitorStationRelationById(Long id);

    /**
     * 批量删除监测站点和类型关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonitorStationRelationByIds(Long[] ids);
}

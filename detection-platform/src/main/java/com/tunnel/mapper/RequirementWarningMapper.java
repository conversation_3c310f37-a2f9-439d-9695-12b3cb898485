package com.tunnel.mapper;

import com.tunnel.domain.CheckDTO;
import com.tunnel.domain.MonitorDTO;
import com.tunnel.domain.RequirementWarning;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 设备报警记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface RequirementWarningMapper 
{
    /**
     * 查询设备报警记录
     * 
     * @param id 设备报警记录主键
     * @return 设备报警记录
     */
    public RequirementWarning selectRequirementWarningById(String id);

    /**
     * 查询设备报警记录列表
     * 
     * @param requirementWarning 设备报警记录
     * @return 设备报警记录集合
     */
    public List<RequirementWarning> selectRequirementWarningList(RequirementWarning requirementWarning);

    /**
     * 新增设备报警记录
     * 
     * @param requirementWarning 设备报警记录
     * @return 结果
     */
    public int insertRequirementWarning(RequirementWarning requirementWarning);

    /**
     * 批量新增设备报警记录
     */
    public int insertBatch(@Param("list") List<RequirementWarning> list);

    /**
     * 修改设备报警记录
     * 
     * @param requirementWarning 设备报警记录
     * @return 结果
     */
    public int updateRequirementWarning(RequirementWarning requirementWarning);

    /**
     * 删除设备报警记录
     * 
     * @param id 设备报警记录主键
     * @return 结果
     */
    public int deleteRequirementWarningById(String id);

    /**
     * 批量删除设备报警记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRequirementWarningByIds(String[] ids);

    /**
     * 根据key查询设备报警记录
     *
     * @param key 对应的业务标识key(b_key)
     * @return
     */
    public List<RequirementWarning> listByKey(@Param(value = "key") String key);

    /**
     * 获取报警数据表的所有字段信息
     *
     * @return 字段信息列表，包含字段名和注释
     */
    public List<Map<String, Object>> getAllFields();

    CheckDTO selectTimeRange(CheckDTO dto);

    List<Map<String, Object>> selectByParams(CheckDTO dto);
}

package com.tunnel.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/8/14  15:30
 * @since 1.0.0
 */
@Getter
public enum MonitorTypeEnum {

    SEWAGE("sewage", "污水类"),

    AIR("air", "大气类"),

    NOISE("noise", "噪声类"),

    SOLID("solid", "固废类")

    ;

    MonitorTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private final String code;
    private final String name;
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.MonitorSewageMapper">

    <resultMap type="MonitorSewage" id="ScMonitorDataResult">
        <result property="id" column="id"/>
        <result property="monitorCode" column="monitor_code"/>
        <result property="bKey" column="b_key"/>
        <result property="topic" column="topic"/>
        <result property="ph" column="ph"/>
        <result property="wt" column="wt"/>
        <result property="cod" column="cod"/>
        <result property="tn" column="tn"/>
        <result property="nh3n" column="nh3n"/>
        <result property="tp" column="tp"/>
        <result property="oil" column="oil"/>
        <result property="ss" column="ss"/>
        <result property="swtp" column="swtp"/>
        <result property="swnh4n" column="swnh4n"/>
        <result property="swcod" column="swcod"/>
        <result property="swoil" column="swoil"/>
        <result property="turb" column="turb"/>
        <result property="flow" column="flow"/>
        <result property="wfloat" column="wfloat"/>
        <result property="wrtflo" column="wrtflo"/>
        <result property="hight" column="hight"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="modifier" column="modifier"/>
    </resultMap>

    <sql id="selectScMonitorDataVo">
        select id, monitor_code, b_key, topic, ph, wt, cod, tn, nh3n, tp, oil, ss, swtp, swnh4n, swcod, swoil, turb, flow, wfloat, wrtflo, hight, remark, create_time, update_time, creator, modifier
        from sc_monitor_sewage
    </sql>

    <select id="selectScMonitorDataList" parameterType="MonitorSewage" resultMap="ScMonitorDataResult">
        <include refid="selectScMonitorDataVo"/>
        <where>
            <if test="monitorCode != null and monitorCode != ''">
                AND monitor_code = #{monitorCode}
            </if>
            <if test="bKey != null and bKey != ''">
                AND b_key = #{bKey}
            </if>
            <if test="createTime != null">
                AND create_time = #{createTime}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectScMonitorDataById" parameterType="Long" resultMap="ScMonitorDataResult">
        <include refid="selectScMonitorDataVo"/>
        where id = #{id}
    </select>
    <select id="selectByParams" resultType="java.util.Map">
        select s.*
       from sc_monitor_sewage s
        where create_time between #{startTime} and  #{endTime}
        and monitor_code = #{systemCode}
        order by create_time desc
    </select>
    <select id="selectTimeRange" resultType="com.tunnel.domain.MonitorDTO">
        select max(create_time) endTime from sc_monitor_sewage where  monitor_code = #{systemCode}
    </select>

    <insert id="insertScMonitorData" parameterType="MonitorSewage" useGeneratedKeys="true" keyProperty="id">
        insert into sc_monitor_sewage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="monitorCode != null">monitor_code,</if>
            <if test="bKey != null">b_key,</if>
            <if test="topic != null">topic,</if>
            <if test="ph != null">ph,</if>
            <if test="wt != null">wt,</if>
            <if test="cod != null">cod,</if>
            <if test="tn != null">tn,</if>
            <if test="nh3n != null">nh3n,</if>
            <if test="tp != null">tp,</if>
            <if test="oil != null">oil,</if>
            <if test="ss != null">ss,</if>
            <if test="swtp != null">swtp,</if>
            <if test="swnh4n != null">swnh4n,</if>
            <if test="swcod != null">swcod,</if>
            <if test="swoil != null">swoil,</if>
            <if test="turb != null">turb,</if>
            <if test="flow != null">flow,</if>
            <if test="wfloat != null">wfloat,</if>
            <if test="wrtflo != null">wrtflo,</if>
            <if test="hight != null">hight,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="monitorCode != null">#{monitorCode},</if>
            <if test="bKey != null">#{bKey},</if>
            <if test="topic != null">#{topic},</if>
            <if test="ph != null">#{ph},</if>
            <if test="wt != null">#{wt},</if>
            <if test="cod != null">#{cod},</if>
            <if test="tn != null">#{tn},</if>
            <if test="nh3n != null">#{nh3n},</if>
            <if test="tp != null">#{tp},</if>
            <if test="oil != null">#{oil},</if>
            <if test="ss != null">#{ss},</if>
            <if test="swtp != null">#{swtp},</if>
            <if test="swnh4n != null">#{swnh4n},</if>
            <if test="swcod != null">#{swcod},</if>
            <if test="swoil != null">#{swoil},</if>
            <if test="turb != null">#{turb},</if>
            <if test="flow != null">#{flow},</if>
            <if test="wfloat != null">#{wfloat},</if>
            <if test="wrtflo != null">#{wrtflo},</if>
            <if test="hight != null">#{hight},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into sc_monitor_sewage (monitor_code, b_key, topic, ph, wt, cod, tn, nh3n, tp, oil, ss, swtp, swnh4n, swcod, swoil, turb, flow, wfloat, wrtflo, hight, remark, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.monitorCode}, #{item.bKey}, #{item.topic}, #{item.ph}, #{item.wt}, #{item.cod}, #{item.tn}, #{item.nh3n}, #{item.tp}, #{item.oil}, #{item.ss}, #{item.swtp}, #{item.swnh4n}, #{item.swcod}, #{item.swoil}, #{item.turb}, #{item.flow}, #{item.wfloat}, #{item.wrtflo}, #{item.hight}, #{item.remark}, #{item.createTime})
        </foreach>
    </insert>

    <update id="updateScMonitorData" parameterType="MonitorSewage">
        update sc_monitor_sewage
        <trim prefix="SET" suffixOverrides=",">
            <if test="monitorCode != null">monitor_code = #{monitorCode},</if>
            <if test="bKey != null">b_key = #{bKey},</if>
            <if test="topic != null">topic = #{topic},</if>
            <if test="ph != null">ph = #{ph},</if>
            <if test="wt != null">wt = #{wt},</if>
            <if test="cod != null">cod = #{cod},</if>
            <if test="tn != null">tn = #{tn},</if>
            <if test="nh3n != null">nh3n = #{nh3n},</if>
            <if test="tp != null">tp = #{tp},</if>
            <if test="oil != null">oil = #{oil},</if>
            <if test="ss != null">ss = #{ss},</if>
            <if test="swtp != null">swtp = #{swtp},</if>
            <if test="swnh4n != null">swnh4n = #{swnh4n},</if>
            <if test="swcod != null">swcod = #{swcod},</if>
            <if test="swoil != null">swoil = #{swoil},</if>
            <if test="turb != null">turb = #{turb},</if>
            <if test="flow != null">flow = #{flow},</if>
            <if test="wfloat != null">wfloat = #{wfloat},</if>
            <if test="wrtflo != null">wrtflo = #{wrtflo},</if>
            <if test="hight != null">hight = #{hight},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScMonitorDataById" parameterType="Long">
        delete from sc_monitor_sewage where id = #{id}
    </delete>

    <delete id="deleteScMonitorDataByIds" parameterType="Long">
        delete from sc_monitor_sewage where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>

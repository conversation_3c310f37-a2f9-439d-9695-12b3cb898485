<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.MonitorStationRelationMapper">
    
    <resultMap type="com.tunnel.domain.MonitorStationRelation" id="MonitorStationRelationResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="monitorType" column="monitor_type" />
        <result property="monitorTypeName" column="monitor_type_name" />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectMonitorStationRelationVo">
        id, code, monitor_type, monitor_type_name, remark, create_time, update_time, creator, modifier
    </sql>

    <select id="selectMonitorStationRelationList" parameterType="com.tunnel.domain.MonitorStationRelation" resultMap="MonitorStationRelationResult">
        select <include refid="selectMonitorStationRelationVo"/> from <include refid="tableName"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="modifier != null "> and modifier = #{modifier}</if>
        </where>
    </select>
    
    <select id="selectMonitorStationRelationById" parameterType="Long" resultMap="MonitorStationRelationResult">
        select <include refid="selectMonitorStationRelationVo"/> from <include refid="tableName"/>
        where id = #{id}
    </select>
        
    <insert id="insertMonitorStationRelation" parameterType="com.tunnel.domain.MonitorStationRelation" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="monitorType != null">monitor_type,</if>
            <if test="monitorTypeName != null">monitor_type_name,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="monitorType != null">#{monitorType},</if>
            <if test="monitorTypeName != null">#{monitorTypeName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateMonitorStationRelation" parameterType="com.tunnel.domain.MonitorStationRelation">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="monitorType != null and monitorType != ''">monitor_type = #{monitorType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorStationRelationById" parameterType="Long">
        delete from <include refid="tableName"/> where id = #{id}
    </delete>

    <delete id="deleteMonitorStationRelationByIds" parameterType="String">
        delete from <include refid="tableName"/> where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="tableName">
        sc_monitor_station_relation
    </sql>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.GatewayMapper">
    
    <resultMap type="com.tunnel.domain.Gateway" id="ProcessingGatewayResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="mac"    column="mac"    />
        <result property="name"    column="name"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="allColumn">
        id, code, mac, name, remark, create_time, update_time, creator, modifier
    </sql>

    <select id="selectProcessingGatewayList" parameterType="com.tunnel.domain.Gateway" resultMap="ProcessingGatewayResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="mac != null  and mac != ''"> and mac = #{mac}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="modifier != null "> and modifier = #{modifier}</if>
        </where>
    </select>
    
    <select id="selectProcessingGatewayById" parameterType="Long" resultMap="ProcessingGatewayResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        where id = #{id}
    </select>
        
    <insert id="insertProcessingGateway" parameterType="com.tunnel.domain.Gateway" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="mac != null">mac,</if>
            <if test="name != null">name,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="mac != null">#{mac},</if>
            <if test="name != null">#{name},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateProcessingGateway" parameterType="com.tunnel.domain.Gateway">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="mac != null">mac = #{mac},</if>
            <if test="name != null">name = #{name},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProcessingGatewayById" parameterType="Long">
        delete from <include refid="tableName"/> where id = #{id}
    </delete>

    <delete id="deleteProcessingGatewayByIds" parameterType="String">
        delete from <include refid="tableName"/> where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectProcessingGatewayAll" resultMap="ProcessingGatewayResult">
        select <include refid="allColumn"/>
            from <include refid="tableName"/>
    </select>
    <select id="selectByCode" resultType="com.tunnel.domain.Gateway">
        select *
        from sc_gateway
        where code = #{code}
    </select>

    <sql id="tableName">
        sc_gateway
    </sql>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.MonitorStationUserRelationMapper">
    
    <resultMap type="com.tunnel.domain.MonitorStationUserRelation" id="MonitorStationUserRelResult">
        <result property="id"    column="id"    />
        <result property="serviceCode"    column="service_code"    />
        <result property="userName"    column="user_name"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="allColumn">
        id, service_code, user_name, remark, create_time, update_time, creator, modifier
    </sql>

    <select id="selectMonitorStationUserRelList" parameterType="com.tunnel.domain.MonitorStationUserRelation" resultMap="MonitorStationUserRelResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        <where>  
            <if test="serviceCode != null  and serviceCode != ''"> and service_code = #{serviceCode}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="modifier != null "> and modifier = #{modifier}</if>
        </where>
    </select>
    
    <select id="selectMonitorStationUserRelById" parameterType="Long" resultMap="MonitorStationUserRelResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        where id = #{id}
    </select>
        
    <insert id="insertMonitorStationUserRel" parameterType="com.tunnel.domain.MonitorStationUserRelation" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceCode != null and serviceCode != ''">service_code,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceCode != null and serviceCode != ''">#{serviceCode},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateMonitorStationUserRel" parameterType="com.tunnel.domain.MonitorStationUserRelation">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceCode != null and serviceCode != ''">service_code = #{serviceCode},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorStationUserRelById" parameterType="Long">
        delete from <include refid="tableName"/> where id = #{id}
    </delete>

    <delete id="deleteMonitorStationUserRelByIds" parameterType="String">
        delete from <include refid="tableName"/> where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="tableName">
        sc_monitor_station_user_relation
    </sql>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.MonitorStationRelationInfoMapper">
    
    <resultMap type="com.tunnel.domain.MonitorStationRelationInfo" id="MonitorStationRelationInfoResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="monitorType"    column="monitor_type"    />
        <result property="monitorTypeName"    column="monitor_type_name"    />
        <result property="designStands"    column="design_stands"    />
        <result property="eiaStands"    column="eia_stands"    />
        <result property="pic"    column="pic"    />
        <result property="pollutions"    column="pollutions"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="allColumn">
        id, code, monitor_type, monitor_type_name, design_stands, eia_stands, pic, pollutions, remark, create_time, update_time, creator, modifier
    </sql>

    <select id="selectMonitorStationRelationInfoList" parameterType="com.tunnel.domain.MonitorStationRelationInfo" resultMap="MonitorStationRelationInfoResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="monitorType != null  and monitorType != ''"> and monitor_type = #{monitorType}</if>
            <if test="designStands != null  and designStands != ''"> and design_stands = #{designStands}</if>
            <if test="eiaStands != null  and eiaStands != ''"> and eia_stands = #{eiaStands}</if>
            <if test="pic != null  and pic != ''"> and pic = #{pic}</if>
            <if test="pollutions != null  and pollutions != ''"> and pollutions = #{pollutions}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="modifier != null "> and modifier = #{modifier}</if>
        </where>
    </select>
    
    <select id="selectMonitorStationRelationInfoById" parameterType="Long" resultMap="MonitorStationRelationInfoResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        where id = #{id}
    </select>
        
    <insert id="insertMonitorStationRelationInfo" parameterType="com.tunnel.domain.MonitorStationRelationInfo" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="monitorType != null and monitorType != ''">monitor_type,</if>
            <if test="monitorTypeName != null and monitorTypeName != ''">monitor_type_name,</if>
            <if test="designStands != null">design_stands,</if>
            <if test="eiaStands != null">eia_stands,</if>
            <if test="pic != null">pic,</if>
            <if test="pollutions != null">pollutions,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="monitorType != null and monitorType != ''">#{monitorType},</if>
            <if test="monitorTypeName != null and monitorTypeName != ''">#{monitorTypeName},</if>
            <if test="designStands != null">#{designStands},</if>
            <if test="eiaStands != null">#{eiaStands},</if>
            <if test="pic != null">#{pic},</if>
            <if test="pollutions != null">#{pollutions},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateMonitorStationRelationInfo" parameterType="com.tunnel.domain.MonitorStationRelationInfo">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="monitorType != null and monitorType != ''">monitor_type = #{monitorType},</if>
            <if test="designStands != null">design_stands = #{designStands},</if>
            <if test="eiaStands != null">eia_stands = #{eiaStands},</if>
            <if test="pic != null">pic = #{pic},</if>
            <if test="pollutions != null">pollutions = #{pollutions},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorStationRelationInfoById" parameterType="Long">
        delete from <include refid="tableName"/> where id = #{id}
    </delete>

    <delete id="deleteMonitorStationRelationInfoByIds" parameterType="String">
        delete from <include refid="tableName"/> where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="tableName">
        sc_monitor_station_relation_info
    </sql>
</mapper>
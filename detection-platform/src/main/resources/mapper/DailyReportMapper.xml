<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.DailyReportMapper">
    
    <resultMap type="com.tunnel.domain.DailyReport" id="DailyReportResult">
        <result property="id"    column="id"    />
        <result property="monitorType"    column="monitor_type"    />
        <result property="monitorTypeName"    column="monitor_type_name"/>
        <result property="city"    column="city"    />
        <result property="dateTime"    column="date_time"    />
        <result property="district"    column="district"    />
        <result property="factor"    column="factor"    />
        <result property="factorCode"    column="factor_code"    />
        <result property="integrationSystem"    column="integration_system"    />
        <result property="maxValue"    column="max_value"    />
        <result property="minValue"    column="min_value"    />
        <result property="serviceArea"    column="service_area"    />
        <result property="transmissionRate"    column="transmission_rate"    />
        <result property="uploadRate"    column="upload_rate"    />
        <result property="systemCode"    column="system_code"    />
        <result property="serviceCode"    column="service_code"    />
        <result property="dischargeAmount"    column="discharge_amount"    />
        <result property="averageValue"    column="average_value"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="allColumn">
        id, monitor_type, monitor_type_name, city, date_time, district, factor, factor_code, integration_system, max_value, min_value, service_area, transmission_rate, upload_rate, system_code, service_code, discharge_amount, average_value, remark, create_time, update_time, creator, modifier
    </sql>

    <select id="selectDailyReportList" parameterType="com.tunnel.domain.DailyReport" resultMap="DailyReportResult">
        select
        <include refid="allColumn"/>
        from <include refid="tableName"/>
        <where>  
            <if test="monitorType != null  and monitorType != ''"> and monitor_type = #{monitorType}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="dateTime != null  and dateTime != ''"> and date_time = #{dateTime}</if>
            <if test="district != null  and district != ''"> and district = #{district}</if>
            <if test="factor != null  and factor != ''"> and factor = #{factor}</if>
            <if test="factorCode != null  and factorCode != ''"> and factor_code = #{factorCode}</if>
            <if test="integrationSystem != null  and integrationSystem != ''"> and integration_system = #{integrationSystem}</if>
            <if test="maxValue != null "> and max_value = #{maxValue}</if>
            <if test="minValue != null "> and min_value = #{minValue}</if>
            <if test="serviceArea != null  and serviceArea != ''"> and service_area = #{serviceArea}</if>
            <if test="transmissionRate != null  and transmissionRate != ''"> and transmission_rate = #{transmissionRate}</if>
            <if test="uploadRate != null  and uploadRate != ''"> and upload_rate = #{uploadRate}</if>
            <if test="systemCode != null  and systemCode != ''"> and system_code = #{systemCode}</if>
            <if test="serviceCode != null  and serviceCode != ''"> and service_code = #{serviceCode}</if>
            <if test="dischargeAmount != null  and dischargeAmount != ''"> and discharge_amount = #{dischargeAmount}</if>
            <if test="averageValue != null "> and average_value = #{averageValue}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="modifier != null "> and modifier = #{modifier}</if>
        </where>
    </select>
    
    <select id="selectDailyReportById" parameterType="Long" resultMap="DailyReportResult">
        select
        <include refid="allColumn"/>
        from <include refid="tableName"/>
        where id = #{id}
    </select>
        
    <insert id="insertDailyReport" parameterType="com.tunnel.domain.DailyReport" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="monitorType != null">monitor_type,</if>
            <if test="monitorTypeName != null">monitor_type_name,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="dateTime != null and dateTime != ''">date_time,</if>
            <if test="district != null and district != ''">district,</if>
            <if test="factor != null and factor != ''">factor,</if>
            <if test="factorCode != null and factorCode != ''">factor_code,</if>
            <if test="integrationSystem != null and integrationSystem != ''">integration_system,</if>
            <if test="maxValue != null">max_value,</if>
            <if test="minValue != null">min_value,</if>
            <if test="serviceArea != null and serviceArea != ''">service_area,</if>
            <if test="transmissionRate != null">transmission_rate,</if>
            <if test="uploadRate != null">upload_rate,</if>
            <if test="systemCode != null">system_code,</if>
            <if test="serviceCode != null and serviceCode != ''">service_code,</if>
            <if test="dischargeAmount != null">discharge_amount,</if>
            <if test="averageValue != null">average_value,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="monitorType != null">#{monitorType},</if>
            <if test="monitorTypeName != null">#{monitorTypeName},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="dateTime != null and dateTime != ''">#{dateTime},</if>
            <if test="district != null and district != ''">#{district},</if>
            <if test="factor != null and factor != ''">#{factor},</if>
            <if test="factorCode != null and factorCode != ''">#{factorCode},</if>
            <if test="integrationSystem != null and integrationSystem != ''">#{integrationSystem},</if>
            <if test="maxValue != null">#{maxValue},</if>
            <if test="minValue != null">#{minValue},</if>
            <if test="serviceArea != null and serviceArea != ''">#{serviceArea},</if>
            <if test="transmissionRate != null">#{transmissionRate},</if>
            <if test="uploadRate != null">#{uploadRate},</if>
            <if test="systemCode != null">#{systemCode},</if>
            <if test="serviceCode != null and serviceCode != ''">#{serviceCode},</if>
            <if test="dischargeAmount != null">#{dischargeAmount},</if>
            <if test="averageValue != null">#{averageValue},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateDailyReport" parameterType="com.tunnel.domain.DailyReport">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="monitorType != null">monitor_type = #{monitorType},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="dateTime != null and dateTime != ''">date_time = #{dateTime},</if>
            <if test="district != null and district != ''">district = #{district},</if>
            <if test="factor != null and factor != ''">factor = #{factor},</if>
            <if test="factorCode != null and factorCode != ''">factor_code = #{factorCode},</if>
            <if test="integrationSystem != null and integrationSystem != ''">integration_system = #{integrationSystem},</if>
            <if test="maxValue != null">max_value = #{maxValue},</if>
            <if test="minValue != null">min_value = #{minValue},</if>
            <if test="serviceArea != null and serviceArea != ''">service_area = #{serviceArea},</if>
            <if test="transmissionRate != null">transmission_rate = #{transmissionRate},</if>
            <if test="uploadRate != null">upload_rate = #{uploadRate},</if>
            <if test="systemCode != null">system_code = #{systemCode},</if>
            <if test="serviceCode != null and serviceCode != ''">service_code = #{serviceCode},</if>
            <if test="dischargeAmount != null">discharge_amount = #{dischargeAmount},</if>
            <if test="averageValue != null">average_value = #{averageValue},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDailyReportById" parameterType="Long">
        delete from <include refid="tableName"/>  where id = #{id}
    </delete>

    <delete id="deleteDailyReportByIds" parameterType="String">
        delete from <include refid="tableName"/>  where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="tableName">
        sc_daily_report
    </sql>
</mapper>
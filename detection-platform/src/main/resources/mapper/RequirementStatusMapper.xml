<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.RequirementStatusMapper">
    
    <resultMap type="com.tunnel.domain.RequirementStatus" id="RequirementStatusResult">
        <result property="id"    column="id"    />
        <result property="topic"  column="topic"    />
        <result property="key"    column="b_key"    />
        <result property="gatewayId"    column="gateway_id"    />
        <result property="vb100"    column="vb100"    />
        <result property="vb101"    column="vb101"    />
        <result property="vb102"    column="vb102"    />
        <result property="vb103"    column="vb103"    />
        <result property="vb104"    column="vb104"    />
        <result property="vb105"    column="vb105"    />
        <result property="vb106"    column="vb106"    />
        <result property="vb107"    column="vb107"    />
        <result property="vb108"    column="vb108"    />
        <result property="vb109"    column="vb109"    />
        <result property="vb110"    column="vb110"    />
        <result property="vb111"    column="vb111"    />
        <result property="vb112"    column="vb112"    />
        <result property="vb113"    column="vb113"    />
        <result property="vb114"    column="vb114"    />
        <result property="vb115"    column="vb115"    />
        <result property="vb116"    column="vb116"    />
        <result property="vb117"    column="vb117"    />
        <result property="vb118"    column="vb118"    />
        <result property="vb119"    column="vb119"    />
        <result property="vb120"    column="vb120"    />
        <result property="vb121"    column="vb121"    />
        <result property="vb122"    column="vb122"    />
        <result property="vb123"    column="vb123"    />
        <result property="vb124"    column="vb124"    />
        <result property="vb125"    column="vb125"    />
        <result property="vb126"    column="vb126"    />
        <result property="vb127"    column="vb127"    />
        <result property="vb128"    column="vb128"    />
        <result property="vb129"    column="vb129"    />
         <result property="vb130"    column="vb130"    />
         <result property="vb131"    column="vb131"    />
         <result property="vb132"    column="vb132"    />
         <result property="vb133"    column="vb133"    />
         <result property="vb134"    column="vb134"    />
         <result property="vb135"    column="vb135"    />
         <result property="vb136"    column="vb136"    />
         <result property="vb137"    column="vb137"    />
         <result property="vb138"    column="vb138"    />
         <result property="vb139"    column="vb139"    />
         <result property="vb140"    column="vb140"    />
         <result property="vb141"    column="vb141"    />
         <result property="vb142"    column="vb142"    />
         <result property="vb143"    column="vb143"    />
         <result property="vb144"    column="vb144"    />
         <result property="vb145"    column="vb145"    />
         <result property="vb146"    column="vb146"    />
         <result property="vb147"    column="vb147"    />
         <result property="vb148"    column="vb148"    />
         <result property="vb149"    column="vb149"    />
         <result property="vb150"    column="vb150"    />
         <result property="vb151"    column="vb151"    />
         <result property="vb155"    column="vb155"    />
         <result property="vb156"    column="vb156"    />
         <result property="vb157"    column="vb157"    />
         <result property="vb158"    column="vb158"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectRequirementStatusVo">
        select id, b_key, topic, gateway_id, vb100, vb101, vb102, vb103, vb104, vb105, vb106, vb107, vb108, vb109, vb110, vb111, vb112, vb113, vb114, vb115, vb116, vb117, vb118, vb119, vb120, vb121, vb122, vb123, vb124, vb125, vb126, vb127, vb128, vb129, vb130, vb131,
               vb132, vb133, vb134, vb135, vb136, vb137, vb138, vb139, vb140, vb141, vb142, vb143, vb144, vb145, vb146, vb147, vb148, vb149, vb150, vb151, vb155, vb156, vb157, vb158,
               remark, create_time, update_time, creator, modifier
        from sc_requirement_status
    </sql>

    <select id="selectRequirementStatusList" parameterType="com.tunnel.domain.RequirementStatus" resultMap="RequirementStatusResult">
        <include refid="selectRequirementStatusVo"/>
        <where>  
            <if test="key != null  and key != ''"> and b_key = #{key}</if>
            <if test="topic != null  and topic != ''"> and topic = #{topic}</if>
            <if test="vb100 != null  and vb100 != ''"> and vb100 = #{vb100}</if>
            <if test="vb101 != null  and vb101 != ''"> and vb101 = #{vb101}</if>
            <if test="vb102 != null  and vb102 != ''"> and vb102 = #{vb102}</if>
            <if test="vb103 != null  and vb103 != ''"> and vb103 = #{vb103}</if>
            <if test="vb104 != null  and vb104 != ''"> and vb104 = #{vb104}</if>
            <if test="vb105 != null  and vb105 != ''"> and vb105 = #{vb105}</if>
            <if test="vb106 != null  and vb106 != ''"> and vb106 = #{vb106}</if>
            <if test="vb107 != null  and vb107 != ''"> and vb107 = #{vb107}</if>
            <if test="vb108 != null  and vb108 != ''"> and vb108 = #{vb108}</if>
            <if test="vb109 != null  and vb109 != ''"> and vb109 = #{vb109}</if>
            <if test="vb110 != null  and vb110 != ''"> and vb110 = #{vb110}</if>
            <if test="vb111 != null  and vb111 != ''"> and vb111 = #{vb111}</if>
            <if test="vb112 != null  and vb112 != ''"> and vb112 = #{vb112}</if>
            <if test="vb113 != null  and vb113 != ''"> and vb113 = #{vb113}</if>
            <if test="vb114 != null  and vb114 != ''"> and vb114 = #{vb114}</if>
            <if test="vb115 != null  and vb115 != ''"> and vb115 = #{vb115}</if>
            <if test="vb116 != null  and vb116 != ''"> and vb116 = #{vb116}</if>
            <if test="vb117 != null  and vb117 != ''"> and vb117 = #{vb117}</if>
            <if test="vb118 != null  and vb118 != ''"> and vb118 = #{vb118}</if>
            <if test="vb119 != null  and vb119 != ''"> and vb119 = #{vb119}</if>
            <if test="vb120 != null  and vb120 != ''"> and vb120 = #{vb120}</if>
            <if test="vb121 != null  and vb121 != ''"> and vb121 = #{vb121}</if>
            <if test="vb122 != null  and vb122 != ''"> and vb122 = #{vb122}</if>
            <if test="vb123 != null  and vb123 != ''"> and vb123 = #{vb123}</if>
            <if test="vb124 != null  and vb124 != ''"> and vb124 = #{vb124}</if>
            <if test="vb125 != null  and vb125 != ''"> and vb125 = #{vb125}</if>
            <if test="vb126 != null  and vb126 != ''"> and vb126 = #{vb126}</if>
            <if test="vb127 != null  and vb127 != ''"> and vb127 = #{vb127}</if>
            <if test="vb128 != null  and vb128 != ''"> and vb128 = #{vb128}</if>
            <if test="vb129 != null  and vb129 != ''"> and vb129 = #{vb129}</if>
            <if test="vb130 != null  and vb130 != ''"> and vb130 = #{vb130}</if>
            <if test="vb131 != null  and vb131 != ''"> and vb131 = #{vb131}</if>
        </where>
    </select>
    
    <select id="selectRequirementStatusById" parameterType="String" resultMap="RequirementStatusResult">
        <include refid="selectRequirementStatusVo"/>
        where id = #{id}
    </select>

    <insert id="insertRequirementStatus" parameterType="com.tunnel.domain.RequirementStatus" useGeneratedKeys="true" keyProperty="id">
        insert into sc_requirement_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="key != null and key != ''">b_key,</if>
            <if test="topic != null  and topic != ''">topic,</if>
            <if test="gatewayId != null  and gatewayId != ''">gateway_id,</if>
            <if test="vb100 != null">vb100,</if>
            <if test="vb101 != null">vb101,</if>
            <if test="vb102 != null">vb102,</if>
            <if test="vb103 != null">vb103,</if>
            <if test="vb104 != null">vb104,</if>
            <if test="vb105 != null">vb105,</if>
            <if test="vb106 != null">vb106,</if>
            <if test="vb107 != null">vb107,</if>
            <if test="vb108 != null">vb108,</if>
            <if test="vb109 != null">vb109,</if>
            <if test="vb110 != null">vb110,</if>
            <if test="vb111 != null">vb111,</if>
            <if test="vb112 != null">vb112,</if>
            <if test="vb113 != null">vb113,</if>
            <if test="vb114 != null">vb114,</if>
            <if test="vb115 != null">vb115,</if>
            <if test="vb116 != null">vb116,</if>
            <if test="vb117 != null">vb117,</if>
            <if test="vb118 != null">vb118,</if>
            <if test="vb119 != null">vb119,</if>
            <if test="vb120 != null">vb120,</if>
            <if test="vb121 != null">vb121,</if>
            <if test="vb122 != null">vb122,</if>
            <if test="vb123 != null">vb123,</if>
            <if test="vb124 != null">vb124,</if>
            <if test="vb125 != null">vb125,</if>
            <if test="vb126 != null">vb126,</if>
            <if test="vb127 != null">vb127,</if>
            <if test="vb128 != null">vb128,</if>
            <if test="vb129 != null">vb129,</if>
            <if test="vb130 != null">vb130,</if>
            <if test="vb131 != null">vb131,</if>
            <if test="vb132 != null">vb132,</if>
            <if test="vb133 != null">vb133,</if>
            <if test="vb134 != null">vb134,</if>
            <if test="vb135 != null">vb135,</if>
            <if test="vb136 != null">vb136,</if>
            <if test="vb137 != null">vb137,</if>
            <if test="vb138 != null">vb138,</if>
            <if test="vb139 != null">vb139,</if>
            <if test="vb140 != null">vb140,</if>
            <if test="vb141 != null">vb141,</if>
            <if test="vb142 != null">vb142,</if>
            <if test="vb143 != null">vb143,</if>
            <if test="vb144 != null">vb144,</if>
            <if test="vb145 != null">vb145,</if>
            <if test="vb146 != null">vb146,</if>
            <if test="vb147 != null">vb147,</if>
            <if test="vb148 != null">vb148,</if>
            <if test="vb149 != null">vb149,</if>
            <if test="vb150 != null">vb150,</if>
            <if test="vb151 != null">vb151,</if>
            <if test="vb155 != null">vb155,</if>
            <if test="vb156 != null">vb156,</if>
            <if test="vb157 != null">vb157,</if>
            <if test="vb158 != null">vb158,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="key != null and key != ''">#{key},</if>
            <if test="topic != null  and topic != ''">#{topic},</if>
            <if test="gatewayId != null  and gatewayId != ''">#{gatewayId},</if>
            <if test="vb100 != null">#{vb100},</if>
            <if test="vb101 != null">#{vb101},</if>
            <if test="vb102 != null">#{vb102},</if>
            <if test="vb103 != null">#{vb103},</if>
            <if test="vb104 != null">#{vb104},</if>
            <if test="vb105 != null">#{vb105},</if>
            <if test="vb106 != null">#{vb106},</if>
            <if test="vb107 != null">#{vb107},</if>
            <if test="vb108 != null">#{vb108},</if>
            <if test="vb109 != null">#{vb109},</if>
            <if test="vb110 != null">#{vb110},</if>
            <if test="vb111 != null">#{vb111},</if>
            <if test="vb112 != null">#{vb112},</if>
            <if test="vb113 != null">#{vb113},</if>
            <if test="vb114 != null">#{vb114},</if>
            <if test="vb115 != null">#{vb115},</if>
            <if test="vb116 != null">#{vb116},</if>
            <if test="vb117 != null">#{vb117},</if>
            <if test="vb118 != null">#{vb118},</if>
            <if test="vb119 != null">#{vb119},</if>
            <if test="vb120 != null">#{vb120},</if>
            <if test="vb121 != null">#{vb121},</if>
            <if test="vb122 != null">#{vb122},</if>
            <if test="vb123 != null">#{vb123},</if>
            <if test="vb124 != null">#{vb124},</if>
            <if test="vb125 != null">#{vb125},</if>
            <if test="vb126 != null">#{vb126},</if>
            <if test="vb127 != null">#{vb127},</if>
            <if test="vb128 != null">#{vb128},</if>
            <if test="vb129 != null">#{vb129},</if>
            <if test="vb130 != null">#{vb130},</if>
            <if test="vb131 != null">#{vb131},</if>
            <if test="vb132 != null">#{vb132},</if>
            <if test="vb133 != null">#{vb133},</if>
            <if test="vb134 != null">#{vb134},</if>
            <if test="vb135 != null">#{vb135},</if>
            <if test="vb136 != null">#{vb136},</if>
            <if test="vb137 != null">#{vb137},</if>
            <if test="vb138 != null">#{vb138},</if>
            <if test="vb139 != null">#{vb139},</if>
            <if test="vb140 != null">#{vb140},</if>
            <if test="vb141 != null">#{vb141},</if>
            <if test="vb142 != null">#{vb142},</if>
            <if test="vb143 != null">#{vb143},</if>
            <if test="vb144 != null">#{vb144},</if>
            <if test="vb145 != null">#{vb145},</if>
            <if test="vb146 != null">#{vb146},</if>
            <if test="vb147 != null">#{vb147},</if>
            <if test="vb148 != null">#{vb148},</if>
            <if test="vb149 != null">#{vb149},</if>
            <if test="vb150 != null">#{vb150},</if>
            <if test="vb151 != null">#{vb151},</if>
            <if test="vb155 != null">#{vb155},</if>
            <if test="vb156 != null">#{vb156},</if>
            <if test="vb157 != null">#{vb157},</if>
            <if test="vb158 != null">#{vb158},</if>
         </trim>
    </insert>

    <insert id="insertBatch">
        insert into sc_requirement_status (
            b_key, topic, gateway_id,
            vb100, vb101, vb102, vb103, vb104, vb105, vb106, vb107, vb108, vb109,
            vb110, vb111, vb112, vb113, vb114, vb115, vb116, vb117, vb118,
            vb119, vb120, vb121, vb122, vb123, vb124, vb125, vb126, vb127, vb128, vb129,
            vb130, vb131, vb132, vb133, vb134, vb135, vb136, vb137, vb138, vb139, vb140, vb141
        , vb142, vb143, vb144, vb145, vb146, vb147, vb148, vb149, vb150, vb151, vb155, vb156, vb157, vb158,
        create_time
        ) values
        <foreach collection="list" item="it" separator=",">
            (
                #{it.key}, #{it.topic}, #{it.gatewayId},
                #{it.vb100}, #{it.vb101}, #{it.vb102}, #{it.vb103}, #{it.vb104}, #{it.vb105}, #{it.vb106}, #{it.vb107}, #{it.vb108}, #{it.vb109},
                #{it.vb110}, #{it.vb111}, #{it.vb112}, #{it.vb113}, #{it.vb114}, #{it.vb115}, #{it.vb116}, #{it.vb117}, #{it.vb118},
                #{it.vb119}, #{it.vb120}, #{it.vb121}, #{it.vb122}, #{it.vb123}, #{it.vb124}, #{it.vb125}, #{it.vb126}, #{it.vb127}, #{it.vb128}, #{it.vb129},
                #{it.vb130}, #{it.vb131}, #{it.vb132}, #{it.vb133}, #{it.vb134}, #{it.vb135}, #{it.vb136}, #{it.vb137}, #{it.vb138}, #{it.vb139}, #{it.vb140}, #{it.vb141}, #{it.vb142}, #{it.vb143}, #{it.vb144}, #{it.vb145}
            , #{it.vb146}, #{it.vb147}, #{it.vb148}, #{it.vb149}, #{it.vb150}, #{it.vb151}, #{it.vb155}, #{it.vb156}, #{it.vb157}, #{it.vb158}, #{it.createTime}
            )
        </foreach>
    </insert>

    <update id="updateRequirementStatus" parameterType="com.tunnel.domain.RequirementStatus">
        update sc_requirement_status
        <trim prefix="SET" suffixOverrides=",">
            <if test="key != null and key != ''">b_key = #{key},</if>
            <if test="topic != null  and topic != ''">topic = #{topic},</if>
            <if test="vb100 != null">vb100 = #{vb100},</if>
            <if test="vb101 != null">vb101 = #{vb101},</if>
            <if test="vb102 != null">vb102 = #{vb102},</if>
            <if test="vb103 != null">vb103 = #{vb103},</if>
            <if test="vb104 != null">vb104 = #{vb104},</if>
            <if test="vb105 != null">vb105 = #{vb105},</if>
            <if test="vb106 != null">vb106 = #{vb106},</if>
            <if test="vb107 != null">vb107 = #{vb107},</if>
            <if test="vb108 != null">vb108 = #{vb108},</if>
            <if test="vb109 != null">vb109 = #{vb109},</if>
            <if test="vb110 != null">vb110 = #{vb110},</if>
            <if test="vb111 != null">vb111 = #{vb111},</if>
            <if test="vb112 != null">vb112 = #{vb112},</if>
            <if test="vb113 != null">vb113 = #{vb113},</if>
            <if test="vb114 != null">vb114 = #{vb114},</if>
            <if test="vb115 != null">vb115 = #{vb115},</if>
            <if test="vb116 != null">vb116 = #{vb116},</if>
            <if test="vb117 != null">vb117 = #{vb117},</if>
            <if test="vb118 != null">vb118 = #{vb118},</if>
            <if test="vb119 != null">vb119 = #{vb119},</if>
            <if test="vb120 != null">vb120 = #{vb120},</if>
            <if test="vb121 != null">vb121 = #{vb121},</if>
            <if test="vb122 != null">vb122 = #{vb122},</if>
            <if test="vb123 != null">vb123 = #{vb123},</if>
            <if test="vb124 != null">vb124 = #{vb124},</if>
            <if test="vb125 != null">vb125 = #{vb125},</if>
            <if test="vb126 != null">vb126 = #{vb126},</if>
            <if test="vb127 != null">vb127 = #{vb127},</if>
            <if test="vb128 != null">vb128 = #{vb128},</if>
            <if test="vb129 != null">vb129 = #{vb129},</if>
            <if test="vb130 != null">vb130 = #{vb130},</if>
            <if test="vb131 != null">vb131 = #{vb131},</if>
            <choose>
                <when test="updateTime != null">update_time = #{updateTime},</when>
                <otherwise>update_time = now(),</otherwise>
            </choose>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRequirementStatusById" parameterType="String">
        delete from sc_requirement_status where id = #{id}
    </delete>

    <delete id="deleteRequirementStatusByIds" parameterType="String">
        delete from sc_requirement_status where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="listByKey" resultMap="RequirementStatusResult">
        <include refid="selectRequirementStatusVo"/>
            where b_key = #{key}
    </select>

    <select id="getAllFields" resultType="java.util.Map">
        SELECT
            COLUMN_NAME as fieldName,
            COLUMN_COMMENT as fieldComment
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = (SELECT DATABASE())
        AND TABLE_NAME = 'sc_requirement_status'
        AND COLUMN_NAME NOT IN  ('id','gateway_id','b_key','topic', 'remark', 'create_time', 'update_time', 'creator', 'modifier')
        AND COLUMN_COMMENT IS NOT NULL
        AND COLUMN_COMMENT != ''
        ORDER BY ORDINAL_POSITION
    </select>
    <select id="selectTimeRange" resultType="com.tunnel.domain.CheckDTO">
        select max(create_time) endTime
        from sc_requirement_status where  gateway_id = #{gatewayId}
    </select>
    <select id="selectByParams" resultType="java.util.Map">
        select s.*
        from sc_requirement_status s
        where create_time between #{startTime} and  #{endTime}
          and gateway_id = #{gatewayId}
        order by create_time desc
    </select>
</mapper>
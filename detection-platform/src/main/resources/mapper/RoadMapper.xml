<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.RoadMapper">

    <resultMap type="com.tunnel.domain.Road" id="RoadResult">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="year" column="year"/>
        <result property="companyName" column="company_name"/>
        <result property="roadCode" column="road_code"/>
        <result property="roadName" column="road_name"/>
        <result property="startCode" column="start_code"/>
        <result property="endCode" column="end_code"/>
        <result property="mileage" column="mileage"/>
        <result property="manager" column="manager"/>
        <result property="managerPhone" column="manager_phone"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="modifier" column="modifier"/>
        <result property="versionNo" column="version_no"/>
    </resultMap>

    <sql id="selectRoadVo">
        select r.id, r.code, r.year, r.company_name, r.road_code, r.road_name, r.start_code, r.end_code, r.mileage, r.manager, r.manager_phone, r.remark,
               r.create_time, r.update_time, r.creator, r.modifier
        from sc_road r
        left join sys_user u on r.creator = u.user_id
        left join sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectRoadList" parameterType="com.tunnel.domain.Road" resultMap="RoadResult">
        <include refid="selectRoadVo"/>
        <where>
            <if test="year != null">and year = #{year}</if>
            <if test="companyName != null and companyName != ''">and company_name like concat('%', #{companyName}, '%')</if>
            <if test="roadCode != null and roadCode != ''">and road_code like concat('%', #{roadCode}, '%')</if>
            <if test="roadName != null and roadName != ''">and road_name like concat('%', #{roadName}, '%')</if>
            <if test="startCode != null and startCode != ''">and start_code = #{startCode}</if>
            <if test="endCode != null and endCode != ''">and end_code = #{endCode}</if>
            <if test="manager != null and manager != ''">and manager like concat('%', #{manager}, '%')</if>
            <if test="managerPhone != null and managerPhone != ''">and manager_phone like concat('%', #{managerPhone}, '%')</if>
            <if test="roadCodeOrName != null and roadCodeOrName != ''">and ( road_code like concat('%', #{roadCodeOrName}, '%') or road_name like concat('%', #{roadCodeOrName}, '%') )</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        order by create_time desc
    </select>

    <select id="selectRoadById" parameterType="Long" resultMap="RoadResult">
        <include refid="selectRoadVo"/>
        where id = #{id}
    </select>

    <select id="selectRoadByCode" parameterType="String" resultMap="RoadResult">
        <include refid="selectRoadVo"/>
        where road_code = #{roadCode} 
    </select>

    <select id="selectRoadListByUserId" parameterType="Long" resultMap="RoadResult">
        <include refid="selectRoadVo"/>
        where user_id = #{userId}  and is_available = 1
        order by create_time desc
    </select>

    <select id="selectRoadListByDeptId" parameterType="Long" resultMap="RoadResult">
        <include refid="selectRoadVo"/>
        where dept_id = #{deptId}  and is_available = 1
        order by create_time desc
    </select>

    <select id="checkRoadCodeUnique" parameterType="com.tunnel.domain.Road" resultMap="RoadResult">
        <include refid="selectRoadVo"/>
        where year = #{year} and company_name = #{companyName} and road_code = #{roadCode} 
        and start_code = #{startCode} and end_code = #{endCode} 
        <if test="id != null">and id != #{id}</if>
        limit 1
    </select>

    <insert id="insertRoad" parameterType="com.tunnel.domain.Road" useGeneratedKeys="true" keyProperty="id">
        insert into sc_road
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="year != null">year,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="roadCode != null and roadCode != ''">road_code,</if>
            <if test="roadName != null and roadName != ''">road_name,</if>
            <if test="startCode != null and startCode != ''">start_code,</if>
            <if test="endCode != null and endCode != ''">end_code,</if>
            <if test="mileage != null">mileage,</if>
            <if test="manager != null and manager != ''">manager,</if>
            <if test="managerPhone != null and managerPhone != ''">manager_phone,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null">creator,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="year != null">#{year},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="roadCode != null and roadCode != ''">#{roadCode},</if>
            <if test="roadName != null and roadName != ''">#{roadName},</if>
            <if test="startCode != null and startCode != ''">#{startCode},</if>
            <if test="endCode != null and endCode != ''">#{endCode},</if>
            <if test="mileage != null">#{mileage},</if>
            <if test="manager != null and manager != ''">#{manager},</if>
            <if test="managerPhone != null and managerPhone != ''">#{managerPhone},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null">#{creator},</if>
        </trim>
    </insert>

    <update id="updateRoad" parameterType="com.tunnel.domain.Road">
        update sc_road
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null">code = #{code},</if>
            <if test="year != null">year = #{year},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="roadCode != null and roadCode != ''">road_code = #{roadCode},</if>
            <if test="roadName != null and roadName != ''">road_name = #{roadName},</if>
            <if test="startCode != null and startCode != ''">start_code = #{startCode},</if>
            <if test="endCode != null and endCode != ''">end_code = #{endCode},</if>
            <if test="mileage != null">mileage = #{mileage},</if>
            <if test="manager != null">manager = #{manager},</if>
            <if test="managerPhone != null">manager_phone = #{managerPhone},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRoadById" parameterType="Long">
        update sc_road set  where id = #{id}
    </delete>

    <delete id="deleteRoadByIds" parameterType="Long">
        update sc_road set  where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countRoad" parameterType="com.tunnel.domain.Road" resultType="int">
        select count(*)
        from sc_road r
        left join sys_user u on r.creator = u.user_id
        left join sys_dept d on u.dept_id = d.dept_id
        <where>
            <if test="year != null">and year = #{year}</if>
            <if test="companyName != null and companyName != ''">and company_name like concat('%', #{companyName}, '%')</if>
            <if test="roadCode != null and roadCode != ''">and road_code like concat('%', #{roadCode}, '%')</if>
            <if test="roadName != null and roadName != ''">and road_name like concat('%', #{roadName}, '%')</if>
            <if test="startCode != null and startCode != ''">and start_code = #{startCode}</if>
            <if test="endCode != null and endCode != ''">and end_code = #{endCode}</if>
            <if test="manager != null and manager != ''">and manager like concat('%', #{manager}, '%')</if>
            <if test="managerPhone != null and managerPhone != ''">and manager_phone like concat('%', #{managerPhone}, '%')</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
    </select>

    <select id="selectAllCompany" resultType="string">
        select distinct company_name from sc_road r
          left join sys_user u on r.creator = u.user_id
          left join sys_dept d on u.dept_id = d.dept_id
        <where>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
    </select>
    <select id="selectRoadByObject" resultType="com.tunnel.domain.Road">
        select * from sc_road
        where year = #{year} and company_name = #{companyName} and road_code = #{roadCode}
        and start_code = #{startCode} and end_code = #{endCode}
    </select>

    <select id="selectRoadByCompanyAndCode" resultMap="RoadResult">
        <include refid="selectRoadVo"/>
        where company_name = #{companyName} and road_code = #{roadCode}
        limit 1
    </select>

    <select id="selectRoadListByIds" resultMap="RoadResult">
        <include refid="selectRoadVo"/>
        <where>
            id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>
</mapper>
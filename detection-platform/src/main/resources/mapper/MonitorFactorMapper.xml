<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.MonitorFactorMapper">
    
    <resultMap type="com.tunnel.domain.MonitorFactor" id="MonitorFactorResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="maxValue"    column="max_value"    />
        <result property="minValue"    column="min_value"    />
        <result property="name"    column="name"    />
        <result property="sensorId"    column="sensor_id"    />
        <result property="unit"    column="unit"    />
        <result property="monitoringSystemSystemCode"    column="monitoring_system_system_code"    />
        <result property="exceeded"    column="exceeded"    />
        <result property="faulty"    column="faulty"    />
        <result property="factorIndex"    column="factor_index"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="allColumn">
        id, code, max_value, min_value, name, sensor_id, unit, monitoring_system_system_code, exceeded, faulty, factor_index, remark, create_time, update_time, creator, modifier
    </sql>

    <select id="selectMonitorFactorList" parameterType="com.tunnel.domain.MonitorFactor" resultMap="MonitorFactorResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="maxValue != null "> and max_value = #{maxValue}</if>
            <if test="minValue != null "> and min_value = #{minValue}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="sensorId != null  and sensorId != ''"> and sensor_id = #{sensorId}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="monitoringSystemSystemCode != null  and monitoringSystemSystemCode != ''"> and monitoring_system_system_code = #{monitoringSystemSystemCode}</if>
            <if test="exceeded != null "> and exceeded = #{exceeded}</if>
            <if test="faulty != null "> and faulty = #{faulty}</if>
            <if test="factorIndex != null "> and factor_index = #{factorIndex}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="modifier != null "> and modifier = #{modifier}</if>
        </where>
    </select>
    
    <select id="selectMonitorFactorById" parameterType="Long" resultMap="MonitorFactorResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        where id = #{id}
    </select>
    <select id="selectMonitorFactorBySystemCode" resultType="com.tunnel.domain.MonitorFactor">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        where monitoring_system_system_code = #{systemCode} and type=#{type}
    </select>

    <insert id="insertMonitorFactor" parameterType="com.tunnel.domain.MonitorFactor" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="maxValue != null">max_value,</if>
            <if test="minValue != null">min_value,</if>
            <if test="name != null">name,</if>
            <if test="sensorId != null">sensor_id,</if>
            <if test="unit != null">unit,</if>
            <if test="monitoringSystemSystemCode != null">monitoring_system_system_code,</if>
            <if test="exceeded != null">exceeded,</if>
            <if test="faulty != null">faulty,</if>
            <if test="factorIndex != null">factor_index,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="maxValue != null">#{maxValue},</if>
            <if test="minValue != null">#{minValue},</if>
            <if test="name != null">#{name},</if>
            <if test="sensorId != null">#{sensorId},</if>
            <if test="unit != null">#{unit},</if>
            <if test="monitoringSystemSystemCode != null">#{monitoringSystemSystemCode},</if>
            <if test="exceeded != null">#{exceeded},</if>
            <if test="faulty != null">#{faulty},</if>
            <if test="factorIndex != null">#{factorIndex},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateMonitorFactor" parameterType="com.tunnel.domain.MonitorFactor">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="maxValue != null">max_value = #{maxValue},</if>
            <if test="minValue != null">min_value = #{minValue},</if>
            <if test="name != null">name = #{name},</if>
            <if test="sensorId != null">sensor_id = #{sensorId},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="monitoringSystemSystemCode != null">monitoring_system_system_code = #{monitoringSystemSystemCode},</if>
            <if test="exceeded != null">exceeded = #{exceeded},</if>
            <if test="faulty != null">faulty = #{faulty},</if>
            <if test="factorIndex != null">factor_index = #{factorIndex},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorFactorById" parameterType="Long">
        delete from <include refid="tableName"/> where id = #{id}
    </delete>

    <delete id="deleteMonitorFactorByIds" parameterType="String">
        delete from <include refid="tableName"/> where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="tableName">
        sc_monitor_factor
    </sql>
</mapper>
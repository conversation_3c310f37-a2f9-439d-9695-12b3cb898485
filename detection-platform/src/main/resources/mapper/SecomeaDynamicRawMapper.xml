<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.SecomeaDynamicRawMapper">

    <resultMap id="SecomeaRawRowMap" type="com.tunnel.domain.SecomeaRawRow">
        <id column="id" property="id"/>
        <result column="dateTime" property="dateTime"/>
        <result column="value" property="value"/>
        <result column="pointAddr" property="pointAddr"/>
        <result column="processingGateWay_id" property="gatewayId"/>
    </resultMap>

    <select id="selectBatchFromRaw" resultMap="SecomeaRawRowMap">
        select d.id, d.dateTime, d.value, f.pointAddr, f.processingGateWay_id
        from ${tableName} d
        inner join secomea_field f on f.id = d.secomeaField_id
        where d.status = 0
        <if test="offsetId != null and offsetId != ''">
            and d.id &gt; #{offsetId}
        </if>
        order by d.id
        limit #{limitSize}
    </select>

    <update id="markProcessed">
        update ${tableName}
        set status = 1
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">#{id}</foreach>
    </update>

    <select id="selectDistinctTimes" resultType="java.lang.String">
        select distinct d.dateTime
        from ${tableName} d
        where d.status = 0
        <if test="lastTime != null and lastTime != ''">
            and d.dateTime > #{lastTime}
        </if>
        limit #{limitSize}
    </select>

    <select id="selectByTimes" resultMap="SecomeaRawRowMap">
        select d.id, d.dateTime, d.value, f.pointAddr, f.processingGateWay_id
        from ${tableName} d
        join secomea_field f on f.id = d.secomeaField_id
        where d.status = 0
          and d.dateTime in
        <foreach collection="times" item="t" open="(" close=")" separator=",">#{t}</foreach>
        order by d.dateTime, d.id
    </select>

    <update id="markProcessedByTimes">
        update ${tableName}
        set status = 1
        where status = 0
          and dateTime in
        <foreach collection="times" item="t" open="(" close=")" separator=",">#{t}</foreach>
    </update>

</mapper>



package com.tunnel.common.utils;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teautil.models.RuntimeOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 阿里云短信发送工具类
 */
@Component
@Slf4j
public class AliyunSmsUtils {

    @Resource
    private Client client;

    /**
     * 发送短信
     * @param signName 签名
     * @param templateCode 模板编码
     * @param phone 手机号
     * @param templateParam 参数
     * @return 是否成功
     */
    public boolean sendMessage(String signName, String templateCode, String phone, String templateParam) {
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setSignName(signName)
                .setTemplateCode(templateCode)
                .setPhoneNumbers(phone)
                .setTemplateParam(templateParam);
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            log.info("==> 开始发送短信, phone: {}, signName: {}, templateCode: {}, templateParam: {}", phone, signName, templateCode, templateParam);
            // 发送短信
            SendSmsResponse response = client.sendSmsWithOptions(sendSmsRequest, runtime);
            if (Objects.equals("OK", response.body.code) ) {
                log.info("==> 短信发送成功, response: {}", JSONObject.toJSONString(response));
            } else {
                log.error("==> 短信发送失败, response: {}", JSONObject.toJSONString(response));
            }
            return true;
        } catch (Exception error) {
            log.error("==> 短信发送错误: ", error);
            return false;
        }
    }
}
